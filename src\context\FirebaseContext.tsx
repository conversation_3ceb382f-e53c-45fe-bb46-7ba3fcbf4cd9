"use client";
import React, { useEffect, ReactNode } from "react";
import { initFirebase } from "../../firebaseConfig";

// This is a compatibility hook for any code that might be using useFirebase
// It no longer initializes Firebase since that's handled by FirebaseProvider
export const useFirebase = () => {
  return {}; // Return an empty object for compatibility
};

// This component is a wrapper that ensures Firebase is initialized
// It doesn't actually provide a context anymore, but we keep it for future use
// and to maintain compatibility with the existing code structure
interface FirebaseProviderProps {
  children: ReactNode;
}

export const FirebaseProvider: React.FC<FirebaseProviderProps> = ({ children }) => {
  useEffect(() => {
    // Initialize Firebase only once when the component mounts
    const initializeFirebase = async () => {
      try {
        await initFirebase();
        // Firebase is now initialized
      } catch (error) {
        console.error("Error initializing Firebase in context:", error);
      }
    };

    initializeFirebase();
  }, []);

  // We don't need to wait for Firebase to be initialized
  // since the singleton pattern in firebaseConfig.ts handles that
  return <>{children}</>;
};

export default FirebaseProvider;
