"use client";
import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../theme";
import {
  Check,
  CheckCircle,
  Circle,
  Play,
  X,
  Twitter,
  Facebook,
  Instagram,
  Loader,
} from "react-feather";

import { Star, MessageSquare, Bookmark, MoreHorizontal, ChevronLeft } from "react-feather";
import { useEffect, useRef, useState } from "react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import React from "react";
import { Label } from "@radix-ui/react-label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import useAuth from "@/hook";
import { CommentManager } from "@/services/commentServices";
import { ReportManager } from "@/services/reportServices";
import { toggleStarredPost, getUserById, toggleBookMarks } from "@/services/usersServices";
import { deletePost, updatePostStars } from "@/services/postService";
import { Timestamp } from "firebase/firestore";

import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import Video from "yet-another-react-lightbox/plugins/video";
import AuthSignup from "@/screens/auth";
import { Modal, ModalBody, ModalContent, Tooltip } from "@heroui/react";
import EditPost from "./editPost";
import Link from "next/link";
import { DateFormatterService } from "@/services/dateFormatterService";
import { Copy } from "react-feather";
import ServiceDescription from "@/globalComponents/formatText";
import LazyMedia from "@/components/LazyMedia";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import { generateFileUrl } from "@/lib/utils";

const resonData = ["Inappropriate content", "Legal issue", "Explicit Content"];

const BrowseUserCard = (props: any) => {
  const user = useAuth();
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [openVideo, setOpenVideo] = useState(false);
  const [openImage, setOpenImage] = useState(false);
  const [isResonPost, setIsResonPost] = useState(false);
  const [isDeletePost, setIsDeletePost] = useState(false);
  const [isOpenDelete, setIsOpenDelete] = useState(false);
  const [isOpenShare, setIsOpenShare] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const postsRef = useRef<HTMLDivElement>(null);
  const [toggleSave, setToggleSave] = useState(false);
  const [toggleStar, setToggleStar] = useState(false);
  const [starCount, setStarCount] = useState(32);

  const [isOpenSheet, setIsOpenSheet] = useState(false);
  const [isTrue, setIsTrue] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [showMoreOptionsmodl, setShowMoreOptionsmodl] = useState(false);

  const [isCheck, setIsCheck] = useState(Array(resonData.length).fill(false));
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null); // Track selected icon index
  const [message, setMessage] = useState("");
  const [comments, setComments]: any = useState([]);
  const [triggerEffect, setTriggerEffect] = useState(false);

  const [imageSize, setImageSize] = useState({ width: 1024, height: 768 });
  const [isToggle, setIsToggle] = useState(false);

  const [isVideoLoading, setIsVideoLoading] = useState(true);

  const handleMessageClick = () => {
    // if (!user?.isLogin) {
    //   setIsSigninOpen(true);
    //   return;
    // }
    setIsOpen(true);
  };

  const handleIsCheck = (id: number) => {
    // Only allow one selection at a time
    const newState = Array(resonData.length).fill(false);
    newState[id] = true;
    setIsCheck(newState);
    setReportReason(resonData[id]); // Set the selected reason
  };

  const handleInfoClick = (index: number) => {
    setSelectedIndex(index === selectedIndex ? null : index); // Deselect if clicked again
    setIsTrue(true);
  };

  // ********************************** comments on post ************************

  useEffect(() => {
    const id = sessionStorage.getItem("openPost");
    if (props.cardData.id === id) {
      const msg = sessionStorage.getItem("input");
      setIsOpen(true);
      setMessage(msg as string);
      sessionStorage.removeItem("input");
      sessionStorage.removeItem("openPost");
    }
  }, []);

  const [isDisable, setIsDisable] = useState(false);
  const handlePostComment = async () => {
    if (!user?.isLogin) {
      sessionStorage.setItem("input", message);
      sessionStorage.setItem("openPost", props.cardData.id);
      setIsSigninOpen(true);
      return;
    }
    try {
      setIsDisable(true);
      if (message) {
        const resp = await CommentManager.getInstance().AddComment({
          hidden: false,
          message: message,
          post_id: props.cardData.id,
          user_id: user.userId,
        });
      }
      // console.log(resp);
      handleGetComment();
      // setIsOpen(false);
      setMessage("");
      setIsDisable(false);
    } catch (error) {
      setIsDisable(false);
      alert("Comment failed");
    }
  };

  const handleGetComment = async () => {
    // if (props.cardData) {
    const resp = await CommentManager.getInstance().GetCommentsByPostId(props.cardData.id);
    // }
    // console.log(resp);

    setComments(resp);
  };

  useEffect(() => {
    handleGetComment();
    // console.log(comments);
  }, [props.cardData, triggerEffect]);

  // ************************** report on post  ***************************
  const [reportReason, setReportReason] = useState("");
  const [report, setReport] = useState("");

  const REPORT_STATUS = {
    IDLE: "idle" as const,
    UPLOADING: "uploading" as const,
    SUCCESS: "success" as const,
  };

  type ReportStatus = (typeof REPORT_STATUS)[keyof typeof REPORT_STATUS];
  const [reportStatus, setReportStatus] = useState<ReportStatus>(REPORT_STATUS.IDLE);

  const DELETE_STATUS = {
    IDLE: "idle" as const,
    DELETING: "deleting" as const,
    SUCCESS: "success" as const,
  };

  type DeleteStatus = (typeof DELETE_STATUS)[keyof typeof DELETE_STATUS];
  const [deleteStatus, setDeleteStatus] = useState<DeleteStatus>(DELETE_STATUS.IDLE);

  // Effect to handle report success state
  useEffect(() => {
    if (reportStatus === REPORT_STATUS.SUCCESS) {
      // Wait 1.5 seconds before closing the modal to show success state
      const timer = setTimeout(() => {
        setIsResonPost(false);
        // Reset form and status
        setReportReason("");
        setReport("");
        setReportStatus(REPORT_STATUS.IDLE);
        // Reset checkboxes
        setIsCheck(Array(resonData.length).fill(false));
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [reportStatus]);

  // Effect to handle delete success state
  useEffect(() => {
    if (deleteStatus === DELETE_STATUS.SUCCESS) {
      // Wait 2 seconds before redirecting to show success state
      const timer = setTimeout(() => {
        setIsDeletePost(false);
        setDeleteStatus(DELETE_STATUS.IDLE);
        // Redirect to browse page
        window.location.href = `/browse/${
          props.cardData?.category === "Storytelling" ? "Literature" : props.cardData?.category
        }`;
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [deleteStatus, props.cardData?.category]);

  const handlePostReport = async () => {
    if (!reportReason || !report) {
      alert("Please select a reason and provide a comment.");
      return;
    }

    setReportStatus(REPORT_STATUS.UPLOADING);

    try {
      const resp = await ReportManager.getInstance().reportPost({
        description: report,
        reason: reportReason as any, // Send the exact text as selected
        sub_reason: reportReason as any, // Send the exact text as sub_reason too
        post_id: props.cardData.id,
        reported_user_id: user.userId,
      });

      if (resp == "success") {
        setReportStatus(REPORT_STATUS.SUCCESS);
      } else {
        setReportStatus(REPORT_STATUS.IDLE);
        alert("Failed to submit report. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting report:", error);
      setReportStatus(REPORT_STATUS.IDLE);
      alert("An error occurred while submitting the report.");
    }
  };

  // ********************* Star Post **************************
  const handleStarPost = async () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }

    // Immediately update UI for real-time feedback
    const newToggleState = false; // We're removing the star
    setToggleStar(newToggleState);
    props.cardData.starred -= 1;

    try {
      const response = await toggleStarredPost(user.userId, props.cardData.id, true);
      if (response.success) {
        console.log("Post removed from starred!");
        await updatePostStars(props.cardData.id, false);
      } else {
        // Revert UI changes on failure
        setToggleStar(true);
        props.cardData.starred += 1;
        console.log(response.error);
      }
    } catch (error) {
      // Revert UI changes on error
      setToggleStar(true);
      props.cardData.starred += 1;
      console.error("Error removing star:", error);
    }
  };

  const handleUnStarPost = async () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }

    // Immediately update UI for real-time feedback
    const newToggleState = true; // We're adding the star
    setToggleStar(newToggleState);
    props.cardData.starred += 1;

    try {
      const response = await toggleStarredPost(user.userId, props.cardData.id, false);
      if (response.success) {
        console.log("Post added to starred!");
        await updatePostStars(props.cardData.id, true);
      } else {
        // Revert UI changes on failure
        setToggleStar(false);
        props.cardData.starred -= 1;
        console.log(response.error);
      }
    } catch (error) {
      // Revert UI changes on error
      setToggleStar(false);
      props.cardData.starred -= 1;
      console.error("Error adding star:", error);
    }
  };

  // ********************* BookMarks **************************
  const handleBookmarks = async () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }

    // Immediately update UI for real-time feedback
    setToggleSave(false); // We're removing the bookmark

    try {
      const response = await toggleBookMarks(user.userId, props.cardData.id, true);
      if (response.success) {
        console.log("Post removed from Bookmarks!");
      } else {
        // Revert UI changes on failure
        setToggleSave(true);
        console.log(response.error);
      }
    } catch (error) {
      // Revert UI changes on error
      setToggleSave(true);
      console.error("Error removing bookmark:", error);
    }
  };

  const handleUnBookmarks = async () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }

    // Immediately update UI for real-time feedback
    setToggleSave(true); // We're adding the bookmark

    try {
      const response = await toggleBookMarks(user.userId, props.cardData.id, false);
      if (response.success) {
        console.log("Post added to Bookmarks!");
      } else {
        // Revert UI changes on failure
        setToggleSave(false);
        console.log(response.error);
      }
    } catch (error) {
      // Revert UI changes on error
      setToggleSave(false);
      console.error("Error adding bookmark:", error);
    }
  };

  const handleGetUserIdByPostServiceEvent = async () => {
    if (!props.cardData?.id || !user?.userId) return;

    try {
      const response = await getUserById(user.userId);
      const resp = response.user;

      if (response.success && resp) {
        // Only update state if the values have actually changed
        const isStarred = resp.starredPosts?.includes(props.cardData.id) || false;
        const isBookmarked = resp.post_bookmarked?.includes(props.cardData.id) || false;

        setToggleStar(isStarred);
        setToggleSave(isBookmarked);
        setTriggerEffect((prev) => !prev);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  // Only fetch user data when component mounts or when user/post changes
  useEffect(() => {
    if (user?.userId && props.cardData?.id) {
      handleGetUserIdByPostServiceEvent();
    }
  }, [user.userId, props.cardData?.id]); // Removed circular dependencies

  // convert timestemp into day and month
  const formatFirebaseTimestamp = (timestamp?: Timestamp | null): string => {
    if (!timestamp || !timestamp.seconds) return "N/A"; // Handle missing timestamp

    const date = new Date(timestamp.seconds * 1000); // Convert to JavaScript Date
    return `${date.getDate()} ${date.toLocaleString("en-US", {
      month: "short",
      year: "numeric",
    })}`;
  };

  // share post
  function copyProfileLink(id: string) {
    const category =
      props.cardData?.category === "Storytelling" ? "Literature" : props.cardData?.category || "";
    const encodedCategory = encodeURIComponent(category);
    const url = `https://www.amuzn.app/browse/${encodedCategory}/${id}%20${props.postUserId}`;
    navigator.clipboard
      .writeText(url)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy link: ", err);
      });
  }

  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoUrl, setVideoUrl] = useState("");
  const [imageUrl2, setImageUrl2] = useState("");

  const handleClick = (event: React.MouseEvent<HTMLVideoElement>, vdoUrl: string) => {
    event.preventDefault();
    event.stopPropagation();
    setVideoUrl(vdoUrl);
    setOpenVideo(true);
  };

  // Handle lightbox open for LazyMedia component
  const handleLightboxOpen = (src: string, type: "image" | "video") => {
    if (type === "image") {
      setImageUrl2(src);
      setOpenImage(true);
    } else if (type === "video") {
      setVideoUrl(src);
      setOpenVideo(true);
    }
  };

  const handleDelete = async () => {
    setDeleteStatus(DELETE_STATUS.DELETING);

    try {
      const response = await deletePost(props.cardData.id);

      if (response.success) {
        setDeleteStatus(DELETE_STATUS.SUCCESS);
      } else {
        setDeleteStatus(DELETE_STATUS.IDLE);
      }
    } catch (error) {
      setDeleteStatus(DELETE_STATUS.IDLE);
      console.error("Delete error:", error);
    }
  };

  // Function to render the appropriate content based on report status
  const renderReportContent = () => {
    if (reportStatus === REPORT_STATUS.SUCCESS) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">Report Submitted Successfully!</h2>
          <p className="text-gray-500 text-center">
            Thank you for your report. We will review it and take appropriate action.
          </p>
        </div>
      );
    }

    if (reportStatus === REPORT_STATUS.UPLOADING) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Submitting Your Report...</h2>
          <p className="text-gray-500 text-center">
            Please wait while we process your report. This may take a moment.
          </p>
        </div>
      );
    }

    // Default form content - will be handled in the modal
    return null;
  };

  // Function to render the appropriate content based on delete status
  const renderDeleteContent = () => {
    if (deleteStatus === DELETE_STATUS.SUCCESS) {
      return (
        <div className="flex flex-col items-center justify-center min-h-[200px] py-4">
          <div className="flex flex-col items-center">
            <div className="bg-green-100 p-4 rounded-full mb-4">
              <Check size={48} className="text-green-500" />
            </div>
            <h2 className="text-xl font-bold mb-2">Post Deleted Successfully!</h2>
            <p className="text-gray-500 text-center">
              Your post has been deleted and you will be redirected shortly.
            </p>
          </div>
        </div>
      );
    }

    if (deleteStatus === DELETE_STATUS.DELETING) {
      return (
        <div className="flex flex-col items-center justify-center min-h-[200px] py-4">
          <div className="flex flex-col items-center">
            <div className="bg-blue-50 p-4 rounded-full mb-4">
              <Loader size={48} className="text-primary animate-spin" />
            </div>
            <h2 className="text-xl font-bold mb-2">Deleting Your Post...</h2>
            <p className="text-gray-500 text-center">
              Please wait while we delete your post. This may take a moment.
            </p>
          </div>
        </div>
      );
    }

    // Default confirmation content
    return (
      <div>
        <p className="text-center text-black text-lg">
          Are you sure you would like to delete the post?
        </p>
        <div>
          <Button
            variant="outline"
            className={`rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base ${
              (deleteStatus as DeleteStatus) === DELETE_STATUS.DELETING
                ? "opacity-50 cursor-not-allowed"
                : "cursor-pointer"
            }`}
            onClick={
              (deleteStatus as DeleteStatus) !== DELETE_STATUS.DELETING ? handleDelete : undefined
            }
            disabled={(deleteStatus as DeleteStatus) === DELETE_STATUS.DELETING}
          >
            {(deleteStatus as DeleteStatus) === DELETE_STATUS.DELETING
              ? "Deleting..."
              : "Yes, delete"}
          </Button>
          <Button
            variant="outline"
            className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
            onClick={() => setIsDeletePost(false)}
            disabled={(deleteStatus as DeleteStatus) === DELETE_STATUS.DELETING}
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* <CarouselSize /> */}
      <div>
        <div className="mb-6 flex flex-col relative">
          <div className="min-w-full">
            {Object.entries(themes).map(([_, innerThemeProperties]) => (
              <div key={innerThemeProperties.title}>
                {(props.cardData?.category === "Storytelling"
                  ? "Literature"
                  : props.cardData?.category) === innerThemeProperties.title && (
                  <div>
                    {props.cardData.mediaType === "image" ? (
                      <div>
                        <div
                          className="border-2"
                          style={{
                            borderColor: innerThemeProperties.backgroundColor,
                          }}
                        >
                          <LazyMedia
                            src={generateFileUrl(props.cardData.postFile)}
                            alt={`Post by ${props.profileName}`}
                            type="image"
                            className="w-full h-full object-cover aspect-square"
                            style={{
                              borderColor: innerThemeProperties.backgroundColor,
                            }}
                            placeholderClassName="bg-gray-100"
                            enableLightbox={true}
                            onLightboxOpen={handleLightboxOpen}
                          />
                        </div>
                      </div>
                    ) : props.cardData.mediaType === "video" ? (
                      <div className="relative">
                        <LazyMedia
                          type="video"
                          src={generateFileUrl(props.cardData.postFile)}
                          alt={`Video post by ${props.profileName}`}
                          className="w-full h-full object-cover border-2 aspect-square"
                          style={{
                            borderColor: innerThemeProperties.backgroundColor,
                          }}
                          controls={false}
                          autoPlay={false}
                          muted={true}
                          showPlayIcon={true}
                          onLoad={() => setIsVideoLoading(false)}
                          onError={() => setIsVideoLoading(false)}
                          enableLightbox={true}
                          onLightboxOpen={handleLightboxOpen}
                        />
                      </div>
                    ) : null}
                  </div>
                )}
              </div>
            ))}
            {/* Content Below */}
            <div className="row justify-between mt-2">
              <p className="text-[14px] text-titleLabel">
                {props?.cardData?.geotags[0]
                  ? props?.cardData?.geotags[0]
                  : props?.userloc
                    ? props?.userloc
                    : "No Location"}
              </p>
              <div className="row gap-3">
                <p
                  className={toggleStar ? " text-sm -mr-1" : "text-iconColor text-sm -mr-1"}
                  style={
                    toggleStar
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                >
                  {props?.cardData?.starred}
                </p>
                <Star
                  className={toggleStar ? "cursor-pointer" : "text-iconColor cursor-pointer"}
                  style={
                    toggleStar
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                  onClick={toggleStar ? handleStarPost : handleUnStarPost}
                />
                <MessageSquare
                  className="text-iconColor cursor-pointer"
                  // style={
                  //   toggleSave
                  //     ? {
                  //         color: props.border, // Dynamic text color
                  //         fill: props.border, // Dynamic fill color
                  //       }
                  //     : undefined
                  // }
                  onClick={handleMessageClick}
                />
                <Bookmark
                  className={
                    toggleSave
                      ? "cursor-pointer" // Add static styles here
                      : "text-iconColor cursor-pointer"
                  }
                  style={
                    toggleSave
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                  onClick={toggleSave ? handleBookmarks : handleUnBookmarks}
                />
                <DropdownMenu open={showMoreOptions} onOpenChange={setShowMoreOptions}>
                  <DropdownMenuTrigger asChild>
                    <MoreHorizontal
                      className="text-iconColor cursor-pointer max-md:hidden"
                      onClick={() => setShowMoreOptions(true)}
                    />
                  </DropdownMenuTrigger>
                  {!(props.postUserId == user.userId) ? (
                    <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          setShowMoreOptions(false);
                          setIsOpenShare(true);
                        }}
                      >
                        Share post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel
                        className="text-center font-normal text-base   cursor-pointer"
                        onClick={() => {
                          setShowMoreOptions(false);
                          user.isLogin ? setIsResonPost(true) : setIsSigninOpen(true);
                        }}
                      >
                        Report post
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  ) : (
                    <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          setIsOpenDelete(true), setShowMoreOptions(false);
                        }}
                      >
                        Edit post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          setShowMoreOptions(false);
                          setIsOpenShare(true);
                        }}
                      >
                        Share post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          setIsDeletePost(true), setShowMoreOptions(false);
                        }}
                      >
                        Delete post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel className="text-center font-normal text-base cursor-pointer">
                        Cancel
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  )}
                </DropdownMenu>
                <MoreHorizontal
                  className="text-iconColor cursor-pointer md:hidden"
                  onClick={() => setIsOpenSheet(true)}
                />
              </div>
            </div>
            <div>
              <p className="text-subtitle text-[14px] 12">
                {/* {props?.cardData?.added_at} */}
                {formatFirebaseTimestamp(
                  props?.cardData?.added_at
                    ? props?.cardData?.added_at
                    : props?.cardData?.lastEditDate
                )}
              </p>
              <p className="text-primary line-clamp-2 cursor-pointer" onClick={handleMessageClick}>
                {props?.cardData?.about_project}
              </p>

              {comments.slice(0, 1).map((item: any, index: any) => (
                <p key={index} className=" line-clamp-2" onClick={handleMessageClick}>
                  <span className="font-[600]">{item?.profile_name} : </span>
                  {item?.message}
                </p>
              ))}
              {comments.length > 0 && (
                <p
                  className="text-[14px] text-subtitle cursor-pointer"
                  onClick={handleMessageClick}
                >
                  See all comments ({comments.length})
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
      {/* Comment Modal */}
      <div>
        <Modal
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          placement="auto"
          size="5xl"
          // isDismissable={false}
          // hideCloseButton={true}
        >
          <ModalContent className="px-3 py-5 max-md:py-0 max-md:px-0 rounded-3xl h-[80vh] max-md:rounded-none max-md:h-full max-md:max-h-screen mx-0 max-md:overflow-hidden">
            {(onClose) => (
              <>
                <ModalBody className="p-0 max-md:h-full max-md:flex max-md:flex-col">
                  <div className="max-md:min-w-full pb-4 max-md:pb-0 rounded-3xl max-md:rounded-none max-md:h-full max-md:flex max-md:flex-col">
                    <div className="grid grid-cols-2 gap-4 max-md:grid-cols-1 max-md:h-full max-md:flex max-md:flex-col">
                      <div className="flex flex-col justify-between max-md:hidden">
                        {/* Post Media */}
                        <div className="flex flex-fill justify-center items-center min-h-[65vh] max-h-[65vh] max-md:min-h-[40vh] max-md:max-h-[40vh]">
                          {props.cardData.mediaType === "image" ? (
                            <div>
                              <LazyMedia
                                src={generateFileUrl(props.cardData.postFile)}
                                alt={`Post by ${props.profileName}`}
                                type="image"
                                className="w-full max-h-[60vh] max-md:min-h-[40vh] max-md:max-h-[40vh]"
                                style={{ borderColor: props.border }}
                                placeholderClassName="bg-gray-100"
                              />
                            </div>
                          ) : props.cardData.mediaType === "video" ? (
                            <LazyMedia
                              src={generateFileUrl(props.cardData.postFile)}
                              alt={`Video post by ${props.profileName}`}
                              type="video"
                              className="w-full max-h-[60vh] max-md:min-h-[40vh] max-md:max-h-[40vh]"
                              style={{ borderColor: props.border }}
                              controls={true}
                              autoPlay={true}
                              muted={false}
                              placeholderClassName="bg-gray-800"
                            />
                          ) : null}
                        </div>
                        {/* Post Info & Actions */}
                        <div className="row justify-between mt-2 p-3">
                          <p className="text-subtitle text-[14px]">
                            {formatFirebaseTimestamp(
                              props?.cardData?.lastEditDate
                                ? props?.cardData?.lastEditDate
                                : props?.cardData?.added_at
                            )}
                          </p>
                          <div className="row gap-3">
                            <p
                              className={
                                toggleStar ? " text-sm -mr-1" : "text-iconColor text-sm -mr-1"
                              }
                              style={
                                toggleStar
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                            >
                              {props?.cardData?.starred}
                            </p>
                            <Star
                              className={
                                toggleStar ? "cursor-pointer" : "text-iconColor cursor-pointer"
                              }
                              style={
                                toggleStar
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                              onClick={toggleStar ? handleStarPost : handleUnStarPost}
                            />
                            <MessageSquare className="text-iconColor cursor-pointer" />
                            <Bookmark
                              className={
                                toggleSave
                                  ? "cursor-pointer" // Add static styles here
                                  : "text-iconColor cursor-pointer"
                              }
                              style={
                                toggleSave
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                              onClick={toggleSave ? handleBookmarks : handleUnBookmarks}
                            />
                            <DropdownMenu
                              open={showMoreOptionsmodl}
                              onOpenChange={setShowMoreOptionsmodl}
                            >
                              <DropdownMenuTrigger asChild>
                                <MoreHorizontal
                                  className="text-iconColor cursor-pointer max-md:hidden"
                                  onClick={() => setShowMoreOptionsmodl(true)}
                                />
                              </DropdownMenuTrigger>
                              {!(props.postUserId == user.userId) ? (
                                <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => {
                                      setShowMoreOptionsmodl(false);
                                      setIsOpenShare(true);
                                    }}
                                  >
                                    Share post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base cursor-pointer"
                                    onClick={() => {
                                      setShowMoreOptionsmodl(false);
                                      user.isLogin ? setIsResonPost(true) : setIsSigninOpen(true);
                                    }}
                                  >
                                    Report post
                                  </DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                </DropdownMenuContent>
                              ) : (
                                <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => {
                                      setIsOpenDelete(true), setShowMoreOptionsmodl(false);
                                    }}
                                  >
                                    Edit post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => {
                                      setShowMoreOptionsmodl(false);
                                      setIsOpenShare(true);
                                    }}
                                  >
                                    Share post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => {
                                      setIsDeletePost(true), setShowMoreOptionsmodl(false);
                                    }}
                                  >
                                    Delete post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel className="text-center font-normal text-base cursor-pointer">
                                    Cancel
                                  </DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                </DropdownMenuContent>
                              )}
                            </DropdownMenu>
                            <MoreHorizontal
                              className="text-iconColor cursor-pointer md:hidden"
                              onClick={() => setIsOpenSheet(true)}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col justify-between h-full max-md:h-full max-md:flex-1 max-md:min-h-0 relative">
                        {/* Mobile Header - Show profile info at top on mobile */}
                        <div className="md:hidden px-3 py-2 border-b bg-white sticky top-0 z-10">
                          <div className="flex items-center gap-2">
                            <Link
                              //@ts-ignore
                              href={`/profile/amuzn/${props.profileName?.replace(/\s+/g, "-")}`}
                            >
                              <LazyMedia
                                src={props.profileAvatar}
                                alt="Profile Avatar"
                                type="image"
                                className="w-[40px] h-[40px] rounded-full object-cover"
                                placeholderClassName="bg-gray-200 rounded-full"
                              />
                            </Link>
                            <div>
                              <Link
                                //@ts-ignore
                                href={`/profile/amuzn/${props.profileName?.replace(/\s+/g, "-")}`}
                              >
                                <p className="font-bold text-sm">
                                  {
                                    //@ts-ignore
                                    props.profileName || "Profile Name"
                                  }
                                </p>
                              </Link>
                              <p className="text-[#616770] text-xs">
                                {props?.cardData?.geotags[0]
                                  ? props?.cardData?.geotags[0]
                                  : props?.userloc
                                    ? props?.userloc
                                    : "No Location"}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Content area with scrollbar */}
                        <div className="px-3 max-md:px-4 overflow-y-auto flex-1 max-md:min-h-0 max-h-[calc(65vh-120px)] max-md:max-h-none">
                          <div className="md:sticky md:top-0 bg-white max-md:hidden">
                            <div className="row gap-2 items-start max-md:items-center">
                              <div>
                                <Link
                                  //@ts-ignore
                                  href={`/profile/amuzn/${props.profileName?.replace(/\s+/g, "-")}`}
                                >
                                  <div>
                                    <LazyMedia
                                      src={props.profileAvatar}
                                      alt="Profile Avatar"
                                      type="image"
                                      className="w-[40px] h-[40px] rounded-full object-cover"
                                      placeholderClassName="bg-gray-200 rounded-full"
                                    />
                                  </div>
                                </Link>
                              </div>
                              <div className="flex flex-row gap-2 max-md:flex-col">
                                <Link
                                  //@ts-ignore
                                  href={`/profile/amuzn/${props.profileName?.replace(/\s+/g, "-")}`}
                                >
                                  <p className="font-bold text-nowrap">
                                    {
                                      //@ts-ignore
                                      props.profileName || "Profile Name"
                                    }
                                  </p>
                                </Link>
                                <p className="text-[#616770] max-md:-mt-1">
                                  {props?.cardData?.geotags[0]
                                    ? props?.cardData?.geotags[0]
                                    : props?.userloc
                                      ? props?.userloc
                                      : "No Location"}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="max-md:pb-2">
                            {/* Content with text wrapping */}
                            <div className="text-primary break-words whitespace-pre-wrap mt-2 mb-2 border-b pb-2 md:pl-[40px] max-md:pl-0">
                              <ServiceDescription description={props.cardData?.about_project} />
                            </div>

                            {/* Comments Section */}
                            <div className="mt-4 mb-4 max-md:mb-4">
                              <h3 className="font-bold text-sm mb-2">Comments</h3>
                              {!comments ? (
                                <div className="text-center py-4">Loading comments...</div>
                              ) : comments && comments.length > 0 ? (
                                <div className="space-y-3">
                                  {comments.map((item: any, index: any) => (
                                    <div key={index} className="flex gap-2 mb-2 border-b pb-2">
                                      <div className="flex-shrink-0"></div>
                                      <div className="flex-grow">
                                        <Link
                                          href={`/profile/amuzn/${item.profile_name?.replace(/\s+/g, "-")}`}
                                        >
                                          <p className="font-semibold text-sm">
                                            {item.profile_name}
                                          </p>
                                        </Link>
                                        <div className="break-words whitespace-pre-wrap text-sm">
                                          {item.message}
                                        </div>
                                        <p className="text-xs text-subtitle mt-1">
                                          {DateFormatterService.formatToDateMonth(item.created_at)}
                                        </p>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-center py-4 text-subtitle">
                                  No comments yet. Be the first to comment!
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Comment Input - Fixed positioning for mobile */}
                        <div className="grid w-full gap-1.5 max-md:gap-0 px-3 pt-2 bg-background border-t max-md:sticky max-md:bottom-0 max-md:bg-white max-md:z-20 max-md:border-t-2 max-md:shadow-lg">
                          <Label
                            htmlFor="comment"
                            className="text-primary text-sm font-bold max-md:text-start"
                          >
                            Add a comment
                          </Label>
                          <Textarea
                            placeholder="Write a comment..."
                            id="comment"
                            className="text-primary text-base max-md:min-h-[80px] resize-none"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            onFocus={() => {
                              // Ensure modal stays in proper position on iOS
                              if (typeof window !== "undefined" && window.scrollTo) {
                                setTimeout(() => window.scrollTo(0, 0), 100);
                              }
                            }}
                          />
                          <Badge
                            className="btn-xs btn text-center mt-2 rounded-full w-[100px] min-h-[30px] cursor-pointer max-md:mb-2"
                            variant="outline"
                            onClick={() => {
                              !isDisable ? handlePostComment() : "";
                            }}
                          >
                            {isDisable ? (
                              <span className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></span>
                            ) : (
                              "Post"
                            )}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
      {/* Video LightBox */}
      <Lightbox
        open={openVideo}
        close={() => setOpenVideo(false)}
        plugins={[Video]}
        carousel={{ preload: 0, finite: true }}
        toolbar={{ buttons: ["close"] }}
        controller={{
          closeOnBackdropClick: true,
          touchAction: "none",
        }}
        slides={[
          {
            type: "video",
            width: 1280,
            height: 720,
            controls: true,
            muted: false,
            autoPlay: true, // Changed to false for iPhone compatibility
            playsInline: true, // Important for iPhone
            poster: `${videoUrl}#t=0.1`, // Add poster for better preview
            sources: [
              {
                src: videoUrl as string,
                type: "video/mp4",
              },
            ],
          },
        ]}
        styles={{
          container: {
            background: "rgba(0, 0, 0, 0.6)",
          },
        }}
        video={{
          controls: true,
          playsInline: true, // Ensure inline playback on iPhone
        }}
      />

      {/* Image LightBox */}
      <Lightbox
        open={openImage}
        close={() => setOpenImage(false)}
        carousel={{ preload: 0, finite: true }}
        toolbar={{ buttons: ["close"] }}
        controller={{
          closeOnBackdropClick: true,
          touchAction: "none",
        }}
        plugins={[Zoom]}
        zoom={{
          maxZoomPixelRatio: 3,
          zoomInMultiplier: 2,
          doubleTapDelay: 300,
          doubleClickDelay: 300,
          doubleClickMaxStops: 2,
          keyboardMoveDistance: 50,
          wheelZoomDistanceFactor: 100,
          pinchZoomDistanceFactor: 100,
          scrollToZoom: true,
        }}
        slides={[
          {
            src: imageUrl2,
            width: 1280,
            height: 720,
          },
        ]}
        styles={{
          container: {
            background: "rgba(0, 0, 0, 0.6)",
          },
        }}
      />

      <style>
        {`
          .yarl__navigation_prev {
            display: none !important;
          }
          .yarl__video_container video {
            max-width: max-content !important;
            -webkit-playsinline: true !important;
            playsinline: true !important;
          }
          .yarl__navigation_next {
            display: none !important;
          }
        `}
      </style>

      {/* Bottom Icon  */}
      <Sheet open={isOpenSheet} onOpenChange={setIsOpenSheet}>
        <SheetContent side="bottom" className="bg-transparent border-none">
          {!(props.postUserId == user.userId) ? (
            <div className="bg-white rounded-[14px]">
              <button
                className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
                onClick={() => {
                  setIsOpenSheet(false), setIsOpenShare(true);
                }}
              >
                Share Post
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] rounded-[14px]"
                onClick={() => {
                  if (user.isLogin) {
                    setIsOpenSheet(false);
                    setIsResonPost(true);
                  } else {
                    setIsSigninOpen(true);
                  }
                }}
              >
                Report Post
              </button>
            </div>
          ) : (
            <div className="bg-white rounded-[14px]">
              <button
                className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
                onClick={() => {
                  setIsOpenDelete(true), setIsOpenSheet(false);
                }}
              >
                Edit post
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
                onClick={() => {
                  setIsDeletePost(true), setIsOpenSheet(false);
                }}
              >
                Delete post
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
                onClick={() => {
                  setIsOpenSheet(false), setIsOpenShare(true);
                }}
              >
                Share post
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] rounded-[14px]"
                // onClick={() => {
                //   user.isLogin ? setIsResonPost(true) : setIsSigninOpen(true);
                // }}

                onClick={() => {
                  if (user.isLogin) {
                    setIsOpenSheet(false);
                    setIsResonPost(true);
                  } else {
                    setIsSigninOpen(true);
                  }
                }}
              >
                Report Post
              </button>
            </div>
          )}
          <div className="mt-4">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px] "
              onClick={() => setIsOpenSheet(false)}
            >
              Cancel
            </button>
          </div>
        </SheetContent>
      </Sheet>
      {/* Report Post */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isResonPost}
          placement="auto"
          onOpenChange={setIsResonPost}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {renderReportContent() || (
                    <div className="">
                      <div className=" relative w-full">
                        <div className="row justify-between mb-5">
                          <X
                            onClick={() => setIsResonPost(false)}
                            className="text-primary cursor-pointer"
                          />
                          <p className="font-bold  text-titleLabel ">Report Post</p>
                          <p className=" opacity-0">Test</p>
                        </div>
                        <div className="row max-md:justify-center  max-md:items-start">
                          <div className="min-w-full pb-5 ">
                            <div className="grid w-full gap-1.5 px-3 ">
                              <div className="grid w-full md:max-w-sm items-center gap-1 mt-4 max-md:text-start">
                                <Label
                                  htmlFor="message"
                                  className="text-primary text-base font-bold max-md:text-start"
                                >
                                  Reason
                                </Label>
                                {resonData.map((item, indexs) => (
                                  <div className="grid grid-cols-1 mt-1" key={indexs}>
                                    <div className="row gap-3">
                                      {isCheck[indexs] ? (
                                        <CheckCircle
                                          className="text-primary w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                          size={18}
                                          onClick={() => {
                                            handleIsCheck(indexs), setReportReason(item);
                                          }}
                                        />
                                      ) : (
                                        <Circle
                                          className="text-subtitle w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                          // size={18}
                                          onClick={() => {
                                            handleIsCheck(indexs), setReportReason(item);
                                          }}
                                        />
                                      )}
                                      <p
                                        className={
                                          isCheck[indexs]
                                            ? "text-primary cursor-pointer"
                                            : "text-subtitle cursor-pointer"
                                        }
                                        onClick={() => {
                                          handleIsCheck(indexs), setReportReason(item);
                                        }}
                                      >
                                        {item}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                              <div className="grid items-center gap-1.5 mt-4 max-md:text-start ">
                                <Label
                                  htmlFor="message"
                                  className="text-primary text-base font-bold"
                                >
                                  Report Сomment
                                </Label>

                                <Textarea
                                  placeholder="Report..."
                                  id="message"
                                  className="text-primary text-base w-full"
                                  value={report}
                                  onChange={(e) => setReport(e.target.value)}
                                  disabled={reportStatus === REPORT_STATUS.UPLOADING}
                                />
                              </div>
                              <Badge
                                className={`btn-xs btn text-center mt-5 rounded-full w-[150px] min-h-[30px] ${
                                  reportStatus === REPORT_STATUS.UPLOADING
                                    ? "opacity-50 cursor-not-allowed"
                                    : "cursor-pointer"
                                }`}
                                variant="outline"
                                onClick={
                                  reportStatus !== REPORT_STATUS.UPLOADING
                                    ? handlePostReport
                                    : undefined
                                }
                              >
                                {reportStatus === REPORT_STATUS.UPLOADING
                                  ? "Submitting..."
                                  : "Confirm"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
      {/* Signout Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isDeletePost}
          placement="auto"
          onOpenChange={setIsDeletePost}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>{renderDeleteContent()}</ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
      {/* SignIn Modal */}
      <div className="max-md:h-full px-5">
        <AlertDialog
          open={isSigninOpen}
          onOpenChange={(val: any) => {
            console.log(val, "valvalvalvalval");
            // setIsSigninOpen(val)
          }}
        >
          <AlertDialogTrigger asChild></AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl  max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className=" max-md: overflow-scroll h-full hide-scroll ">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => {
                    sessionStorage.removeItem("input");
                    sessionStorage.removeItem("openPost");
                    setIsSigninOpen(false);
                  }}
                >
                  <X />
                </div>
                <AuthSignup />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      {/* Edit Post Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenDelete}
          placement="auto"
          onOpenChange={setIsOpenDelete}
          hideCloseButton={true}
          scrollBehavior="inside"
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="overflow-y-scroll hide-scroll-custom">
                    <EditPost setIsOpenDelete={setIsOpenDelete} postId={props.cardData.id} />
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Share Post Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenShare}
          placement="auto"
          onOpenChange={setIsOpenShare}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {() => (
              <>
                <ModalBody>
                  <div>
                    <div>
                      <div className="flex items-center justify-between">
                        <div
                          onClick={() => setIsOpenShare(false)}
                          className="p-1.5 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                        >
                          <X size={20} />
                        </div>
                        <h3 className="font-bold text-lg text-primary">Share Post</h3>
                        <div className="w-8"></div>
                      </div>

                      <div className="mt-6">
                        <div className="flex items-center justify-between border border-gray-200 rounded-lg p-3 bg-gray-50">
                          <div className="truncate mr-2 text-gray-700">
                            https://www.amuzn.app/browse/
                            {props.cardData?.category === "Storytelling"
                              ? "Literature"
                              : props.cardData?.category}
                            /{props.cardData.id}%20{props.postUserId}
                          </div>
                          <div
                            className={`cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-all ${
                              isCopied ? "bg-green-100 text-green-600" : ""
                            }`}
                            onClick={() => copyProfileLink(props.cardData.id)}
                          >
                            {isCopied ? <Check size={18} /> : <Copy size={18} />}
                          </div>
                        </div>

                        <p className="text-gray-500 text-sm mt-4 mb-2">Share on social media</p>

                        <div className="grid grid-cols-4 gap-3 mt-2">
                          <a
                            href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                              `https://www.amuzn.app/browse/${
                                props.cardData?.category === "Storytelling"
                                  ? "Literature"
                                  : props.cardData?.category
                              }/${props.cardData.id}%20${props.postUserId}`
                            )}&text=${encodeURIComponent("Check out this post on Amuzn!")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Twitter size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Twitter</span>
                          </a>

                          <a
                            href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                              `https://www.amuzn.app/browse/${
                                props.cardData?.category === "Storytelling"
                                  ? "Literature"
                                  : props.cardData?.category
                              }/${props.cardData.id}%20${props.postUserId}`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Facebook size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Facebook</span>
                          </a>

                          <div
                            onClick={() => {
                              // Instagram doesn't have a direct share URL, so we'll copy the link to clipboard
                              const postUrl = `https://www.amuzn.app/browse/${
                                props.cardData?.category === "Storytelling"
                                  ? "Literature"
                                  : props.cardData?.category
                              }/${props.cardData.id}%20${props.postUserId}`;
                              navigator.clipboard.writeText(postUrl);
                              setIsCopied(true);
                              setTimeout(() => setIsCopied(false), 2000);
                              // Open Instagram in a new tab
                              window.open("https://www.instagram.com/", "_blank");
                            }}
                            className="flex flex-col items-center justify-center cursor-pointer"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Instagram size={24} className="border-0 outline-0" />
                            </div>
                            <span className="text-xs">Instagram</span>
                          </div>

                          {/* WhatsApp share button (replaces Telegram) */}
                          <a
                            href={`https://wa.me/?text=${encodeURIComponent(
                              `Check out this post on Amuzn! https://www.amuzn.app/browse/${
                                props.cardData?.category === "Storytelling"
                                  ? "Literature"
                                  : props.cardData?.category
                              }/${props.cardData.id}%20${props.postUserId}`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              {/* WhatsApp SVG icon */}
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                className="fill-primary"
                              >
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.966-.94 1.164-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.007-.372-.009-.571-.009-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.099 3.2 5.077 4.363.71.306 1.263.489 1.694.625.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z" />
                                <path d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.341 4.997l-1.409 5.151 5.283-1.389c1.475.807 3.153 1.238 4.782 1.238h.001c5.514 0 9.997-4.483 9.997-9.997 0-2.669-1.04-5.178-2.929-7.067-1.889-1.889-4.398-2.93-7.069-2.93zm0 17.995c-1.462 0-2.892-.393-4.13-1.137l-.295-.174-3.134.823.837-3.057-.192-.314c-.822-1.346-1.257-2.899-1.257-4.486 0-4.411 3.588-7.999 7.999-7.999 2.137 0 4.146.833 5.656 2.344 1.511 1.511 2.344 3.52 2.344 5.656 0 4.411-3.588 7.999-7.999 7.999z" />
                              </svg>
                            </div>
                            <span className="text-xs">WhatsApp</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default BrowseUserCard;
