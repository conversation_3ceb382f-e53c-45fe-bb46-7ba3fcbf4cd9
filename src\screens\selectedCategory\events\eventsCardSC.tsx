"use client";
import { useEffect, useState } from "react";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { getEventsByCategory } from "@/services/eventsServices";
import { getUserByEventId } from "@/services/usersServices";
import { User } from "@/services/UserInterface";
import { themes } from "../../../../theme";
import useAuth from "@/hook";
import EventCardSkeleton from "@/components/CardSkeleton/EventCardSkeleton";
import { useRouter } from "next/navigation";
import { useFilter } from "@/context/FilterContext";

const EventsCardSC = (props: any) => {
  const user = useAuth();
  const router = useRouter();
  const { filters, getServiceFilters } = useFilter();
  const [evenstData, setEvenstData] = useState([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [localLoading, setLocalLoading] = useState<boolean>(true);
  const [userLoading, setUserLoading] = useState<string | null>(null);

  // Function to get user and route to profile
  const handleEventClick = async (eventId: string) => {
    setUserLoading(eventId);
    try {
      const response = await getUserByEventId(eventId);
      setUserLoading(null);
      if (response.success && response.users && response.users.length > 0) {
        const user = response.users[0] as User;
        if (user.profile_name) {
          router.push(`/profile/amuzn/${user.profile_name.replace(/\s+/g, "-")}?view=Events`);
        } else {
          alert("User found but profile name is missing.");
        }
      } else {
        alert("User not found for this event.");
      }
    } catch (error) {
      setUserLoading(null);
      alert("Error fetching user for this event.");
    }
  };

  // Function to get user ID for checking against followers
  const handlegetEventsByCategory = async () => {
    setLoading(true);
    try {
      const currentCategory =
        props.themeProperties.title === "My Feed" ? "My Feed" : props.themeProperties.title;

      // Check if filters are applied and if current category is in selected categories
      if (filters.categories && filters.categories.length > 0) {
        if (!filters.categories.includes(currentCategory)) {
          // Current category is not in selected filters, show no data
          setEvenstData([]);
          setLoading(false);
          return;
        }
      }

      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();

      const response = await getEventsByCategory(
        currentCategory,
        currentCategory === "My Feed" ? user.userId : "",
        serviceFilters
      );
      if (response?.success) {
        const eventsdata: any = response?.events;
        // console.log(eventsdata);
        setEvenstData(eventsdata);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching user ID:", error);
    }
  };

  useEffect(() => {
    handlegetEventsByCategory();
  }, [props, user.userId, filters]); // Ensures function runs only when dependencies change

  // Force initial loading state and ensure it persists
  useEffect(() => {
    // Always start with loading state true
    setLocalLoading(true);

    let timer: NodeJS.Timeout;

    // Only transition to non-loading state when data is ready and loading is false
    if (!loading && evenstData !== null) {
      timer = setTimeout(() => {
        setLocalLoading(false);
      }, 1000); // Longer delay to ensure skeleton is visible
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [loading, evenstData]);

  return (
    <>
      {localLoading ? (
        <div className="">
          <EventCardSkeleton count={16} columns={4} showGrid={true} />
        </div>
      ) : (
        <div className="w-full mt-4">
          {Object.entries(themes).map(([themeName, themeProperties]) => (
            <div key={themeName}>
              {props.themeProperties.title === themeProperties.title && (
                <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3">
                  {evenstData?.length > 0 ? (
                    evenstData.map((post: any, index) => (
                      <div key={index} className="mt-4  w-[350px]">
                        <button
                          onClick={() => handleEventClick(post.id)}
                          className="w-full text-left"
                          disabled={userLoading === post.id}
                          style={{ opacity: userLoading === post.id ? 0.6 : 1 }}
                        >
                          <GlobalCardEvents post={post} border={props.border} />
                        </button>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500">No Events available in this category.</p>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default EventsCardSC;
