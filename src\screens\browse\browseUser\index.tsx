import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../theme";
import { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperType } from "swiper/types";
import "swiper/css";
import "swiper/css/navigation";
import { Navigation } from "swiper/modules";
import React, { useCallback, useEffect, useState } from "react";
import BrowseUserCard from "./browseUserCard";
import Link from "next/link";
import useAuth from "@/hook";
import { FollowerManager } from "@/services/followServices";
import { getUserId } from "@/utils/userUtils";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { X } from "react-feather";
import AuthSignup from "@/screens/auth";
import LazyMedia from "@/components/LazyMedia";
import { generateFileUrl } from "@/lib/utils";

const BrowseUser = (props: any) => {
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);

  const user = useAuth();
  const length = props?.postData?.length || 0;
  const swiperRef = useRef<SwiperType | null>(null);
  const [isAlreadySlided, setAlreadySlided] = useState<boolean>(false);

  // Initialize follow state from props
  useEffect(() => {
    setIsFollowing(props?.userData?.is_followed_by_me || false);
  }, [props?.userData?.is_followed_by_me]);

  // Handle follow/unfollow toggle
  const handleFollowToggle = useCallback(async () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }

    if (isLoading) return;

    // IMMEDIATELY toggle the UI state
    const previousState = isFollowing;
    setIsFollowing(!isFollowing);
    setIsLoading(true);

    try {
      const userId = getUserId(user?.userData);
      if (!userId) {
        console.error("User ID is required for follow action");
        setIsFollowing(previousState); // Revert on error
        return;
      }

      let resp;
      if (previousState) {
        // Was following, so unfollow
        resp = await FollowerManager.getInstance().UnfollowByUserId({
          src_id: userId,
          dest_id: props?.userId,
        });
      } else {
        // Was not following, so follow
        resp = await FollowerManager.getInstance().FollowByUserId({
          src_id: userId,
          dest_id: props?.userId,
        });
      }

      if (resp !== "success") {
        // Revert state if API call failed
        setIsFollowing(previousState);
        console.error("Follow/Unfollow API failed");
      }
    } catch (error) {
      console.error("Follow/Unfollow failed:", error);
      // Revert to previous state on error
      setIsFollowing(previousState);
    } finally {
      setIsLoading(false);
    }
  }, [user, isLoading, isFollowing, props?.userId]);

  // Slide to specific post
  useEffect(() => {
    if (swiperRef.current && props.postId && !isAlreadySlided && props.userData.posts?.length > 0) {
      const postIndex = props.userData.posts.findIndex((post: any) => post.id === props.postId);
      if (postIndex !== -1) {
        setAlreadySlided(true);
        const slidesPerView = swiperRef.current.params.slidesPerView as number;
        const centerIndex = Math.max(postIndex - Math.floor(slidesPerView / 4), 0);
        swiperRef.current.slideTo(centerIndex);
      }
    }
  }, [props.postId, props.userData.posts, isAlreadySlided]);

  const [currentIndex, setCurrentIndex] = useState({
    isStart: true,
    isEnd: false,
  });

  const handleSlideChange = () => {
    if (swiperRef.current) {
      setCurrentIndex({
        isStart: swiperRef.current.activeIndex === 0,
        isEnd: swiperRef.current.isEnd,
      });
    }
  };

  const shouldShowNextButton = () => {
    if (typeof window === "undefined") return false;
    if (window.innerWidth >= 1280) return length > 4;
    if (window.innerWidth >= 1024) return length > 3;
    if (window.innerWidth >= 768) return length > 2;
    return false;
  };

  // Early return if user has no posts
  if (!props.userData?.posts || props.userData.posts.length === 0) {
    return null;
  }

  // Follow button component
  const FollowButton = ({ className = "" }: { className?: string }) => {
    if (isFollowing) {
      return (
        <Badge
          className={`btn-xs border-primary btn min-w-20 w-20 ${
            isLoading ? "pointer-events-none opacity-70" : ""
          } ${className}`}
          variant="outline"
          onClick={handleFollowToggle}
        >
          {isLoading ? (
            <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
          ) : (
            "Unfollow"
          )}
        </Badge>
      );
    }

    return (
      <Badge
        className={`btn-xs text-white min-w-20 w-20 ${
          isLoading ? "pointer-events-none opacity-70" : ""
        } ${className}`}
        onClick={handleFollowToggle}
      >
        {isLoading ? (
          <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
        ) : (
          "Follow"
        )}
      </Badge>
    );
  };

  return (
    <>
      {props.userData.posts.length > 0 && (
        <>
          {Object.entries(themes).map(
            ([themeName, themeProperties]) =>
              themeProperties.title ===
                (props.categoryName == "Storytelling" ? "Literature" : props.categoryName) && (
                <div key={themeName}>
                  <div className="flex flex-col relative overflow-x-hidden">
                    {/* Desktop Header */}
                    <div className="row justify-between sticky left-0 max-md:hidden">
                      <div className="row mb-3 gap-3 justify-between min-w-[360px]">
                        <div className="row gap-2">
                          <Link
                            href={`/profile/amuzn/${props?.userData?.profile_name?.replace(/\s+/g, "-")}`}
                          >
                            <LazyMedia
                              src={
                                generateFileUrl(props?.userData?.avatar) ||
                                "/assets/profileAvatar.svg"
                              }
                              alt="Profile Avatar"
                              type="image"
                              className="w-[40px] h-[40px] rounded-full object-cover"
                              placeholderClassName="bg-gray-200 rounded-full"
                            />
                          </Link>
                          <div>
                            <Link
                              href={`/profile/amuzn/${props?.userData?.profile_name?.replace(/\s+/g, "-")}`}
                            >
                              <p className="font-bold text-nowrap">
                                {props?.userData?.profile_name || "Profile Name"}
                              </p>
                            </Link>
                            <p className="text-[#616770] -mt-1">
                              {props?.userData?.location || "Location"}
                            </p>
                          </div>
                        </div>
                        {user.userId !== props?.userId && (
                          <div className="row gap-3">
                            <FollowButton />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Mobile Header */}
                    <div className="row justify-between sticky left-0 md:hidden">
                      <div className="row mb-3 gap-3">
                        <div className="row gap-2">
                          <Link
                            href={`/profile/amuzn/${props?.userData?.profile_name?.replace(/\s+/g, "-")}`}
                          >
                            <LazyMedia
                              src={
                                generateFileUrl(props?.userData?.avatar) ||
                                "/assets/profileAvatar.svg"
                              }
                              alt="Profile Avatar"
                              type="image"
                              className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] rounded-full object-cover"
                              placeholderClassName="bg-gray-200 rounded-full"
                            />
                          </Link>
                          <div>
                            <Link
                              href={`/profile/amuzn/${props?.userData?.profile_name?.replace(/\s+/g, "-")}`}
                            >
                              <p className="font-bold">
                                {props?.userData?.profile_name || "Profile Name"}
                              </p>
                            </Link>
                            <p className="text-[#616770] -mt-[2px]">
                              {props?.userData?.location || "Location"}
                            </p>
                          </div>
                        </div>
                        {user.userId !== props?.userId && <FollowButton />}
                      </div>
                    </div>

                    {/* Gradient overlays */}
                    <div
                      className={`absolute top-0 right-0 bottom-0 w-[80px] bg-gradient-to-l ${
                        !currentIndex.isEnd && shouldShowNextButton() ? "from-white" : ""
                      } pointer-events-none max-md:hidden z-50`}
                    />
                    <div
                      className={`absolute top-12 left-0 bottom-0 w-[80px] bg-gradient-to-r ${
                        !currentIndex.isStart ? "from-white" : ""
                      } pointer-events-none max-md:hidden z-50`}
                    />

                    {/* Swiper */}
                    <div className="relative w-full">
                      <Swiper
                        modules={[Navigation]}
                        onSwiper={(swiper) => (swiperRef.current = swiper)}
                        onSlideChange={handleSlideChange}
                        loop={false}
                        mousewheel={true}
                        slidesPerView={4.2}
                        spaceBetween={20}
                        breakpoints={{
                          1280: { slidesPerView: 4.2, spaceBetween: 15 },
                          1024: { slidesPerView: 3.3, spaceBetween: 15 },
                          768: { slidesPerView: 2.5, spaceBetween: 15 },
                          480: { slidesPerView: 1.7, spaceBetween: 20 },
                          320: { slidesPerView: 1.2, spaceBetween: 20 },
                        }}
                        className="transition-all duration-300 ease-in-out"
                      >
                        {(props.postData || []).map((cardData: any, index: number) => (
                          <SwiperSlide key={index}>
                            <BrowseUserCard
                              cardData={cardData}
                              border={themeProperties.backgroundColor}
                              postUserId={props?.userId}
                              userloc={props?.userData?.location}
                              profileAvatar={
                                generateFileUrl(props?.userData?.avatar) ||
                                "/assets/profileAvatar.svg"
                              }
                              profileName={props?.userData?.user_name || "Profile Name"}
                              localName={props?.userData?.location}
                            />
                          </SwiperSlide>
                        ))}
                      </Swiper>

                      {/* Navigation buttons */}
                      {typeof window !== "undefined" && window.innerWidth >= 480 && (
                        <>
                          {!currentIndex.isStart && (
                            <button
                              className="absolute top-[45%] left-0 h-[100%] w-[84px] z-[999] transform -translate-y-1/2 text-black p-2 rounded-md"
                              onClick={() => swiperRef.current?.slidePrev()}
                              style={{ backgroundColor: "transparent" }}
                            >
                              <LazyMedia
                                src="/assets/ChevronsDown.svg"
                                alt="Previous"
                                type="image"
                                className="h-[50px] w-[50px] rotate-90"
                              />
                            </button>
                          )}

                          {!currentIndex.isEnd && shouldShowNextButton() && (
                            <button
                              className="absolute top-[45%] right-0 h-[100%] w-[84px] z-[999] transform -translate-y-1/2 text-black p-2 rounded-md"
                              onClick={() => swiperRef.current?.slideNext()}
                              style={{ backgroundColor: "transparent" }}
                            >
                              <LazyMedia
                                src="/assets/ChevronsDown.svg"
                                alt="Next"
                                type="image"
                                className="h-[50px] w-[50px] -rotate-90"
                              />
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )
          )}
        </>
      )}

      {/* SignIn Modal */}
      <div className="max-md:h-full px-5">
        <AlertDialog open={isSigninOpen} onOpenChange={setIsSigninOpen}>
          <AlertDialogTrigger asChild>
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className="max-md:overflow-scroll h-full hide-scroll">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => setIsSigninOpen(false)}
                >
                  <X />
                </div>
                <AuthSignup onClose={() => setIsSigninOpen(false)} />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};

export default BrowseUser;
