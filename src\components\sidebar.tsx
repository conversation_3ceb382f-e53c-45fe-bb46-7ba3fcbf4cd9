// "use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Drawer, DrawerContent, Drawer<PERSON><PERSON>, DrawerFooter, DrawerHeader } from "@heroui/drawer";
import { Badge } from "@/components/ui/badge";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import React, { useEffect, useState } from "react";
import {
  Home,
  ShoppingCart,
  ShoppingBag,
  Bell,
  MessageSquare,
  UserPlus,
  HelpCircle,
  File,
  Book,
  Power,
  Settings,
  X,
  Copy,
  Check,
} from "react-feather";
import AuthSignup from "@/screens/auth";
import useAuth from "@/hook";
import AdjacentDrawer from "@/components/ui/adjacent-drawer";
import NestedDrawer from "@/components/ui/nested-drawer";
import { useSignInStore } from "./GlobalSignInButton";
import { closeEvent } from "@/lib/eventEmmiter";
import Link from "next/link";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { useDisconnect, useAccount } from "wagmi";
import HelpNotion from "./notion/helpNotion";
import ReferNotion from "./notion/referNotion";
import PrivacyPolicyNotion from "./notion/privacyPolicyNotion";
import PaymentTermsNotion from "./notion/paymentTermsNotion";
import TermsOfServicesNotion from "./notion/termsOfServicesNotion";
import AboutNotion from "./notion/aboutNotion";
import RichTextFormatter from "./RichTextFormatter";
import Basket from "./basket/Basket";
import Orders from "./orders/Orders";
import ChatList from "./chat/ChatList";
import ChatBox from "./chat/ChatBox";
import Notifications from "./notifications/Notifications";
import { GetSidebarCount } from "@/services/usersServices";
import { Wallet } from "lucide-react";
import { getAuthenticatedUserId, getAuthHeaders } from "@/lib/auth/clientAuth";
import { ConnectEmbeddedProvider } from "./ConnectEmbeddedProvider";
import ConnectDashboard from "./ConnectDashboard";
import StripeAuthFlow from "./StripeAuthFlow";
import { generateFileUrl } from "@/lib/utils";

const tncdata = `# AMUZN App Terms of Service

**Last updated:** 26 July 2023

Please read the terms and conditions carefully before using Our Service.

The following terms of service (these "Terms" or "Terms of Service"), govern your access to and use of the AMUZN mobile application, including any content, functionality and services offered on or through the AMUZN mobile application (the "App").

The App is owned and operated by **Nascent Ventures Ltd** (71-75 Shelton Street, Covent Garden, London WC2H 9JQ). Nascent Ventures Ltd is referred to in these Terms of Services as "Nascent Ventures", "AMUZN" "we" or "us". You are referred to as "you" or the "user" in these terms and conditions.

These Terms of Service constitute a legally binding agreement between you and Nascent Ventures governing your use of the App.

By using the App, registering your details, opening an account, or by clicking to accept or agree to the Terms of Service when this option is made available to you, you accept and agree, on behalf of yourself or on behalf of your employer or any other entity (if applicable), to be bound and abide by these Terms and AMUZN Payment Terms ("Payment Terms"), which is incorporated herein by reference.

You further acknowledge, you have read and understood our Privacy Policy. If you do not want to agree to these Terms or the Privacy Policy, you must not access or use the App.

For more detailed policies surrounding the activity and usage on the App, please access the designated articles herein.`;

type NavItem = {
  icon: React.ReactNode;
  label: string;
  href: string;
  number: number;
  component: string;
  isClose: boolean;
  onClick?: () => boolean;
};

const navItems: NavItem[] = [
  {
    icon: <Home size={28} strokeWidth={1.7} />,
    label: "Home",
    href: "/",
    number: 0,
    component: "Home",
    isClose: true,
  },
  {
    icon: <ShoppingCart size={28} strokeWidth={1.7} />,
    label: "Basket",
    href: "#Services",
    number: 3,
    component: "Basket",
    isClose: false,
  },
  {
    icon: <ShoppingBag size={28} strokeWidth={1.7} />,
    label: "My Orders",
    href: "#Pricing",
    number: 1,
    component: "Orders",
    isClose: false,
  },
  {
    icon: <Wallet size={28} strokeWidth={1.7} />,
    label: "Stripe Account",
    href: "#FAQs",
    number: 0,
    component: "Stripe Account",
    isClose: false,
  },
  {
    icon: <Bell size={28} strokeWidth={1.7} />,
    label: "Notifications",
    href: "#FAQs",
    number: 5,
    component: "Notifications",
    isClose: false,
  },
  {
    icon: <MessageSquare size={28} strokeWidth={1.7} />,
    label: "Chats",
    href: "#FAQs",
    number: 2,
    component: "ChatList",
    isClose: false,
  },
  {
    icon: <UserPlus size={28} strokeWidth={1.7} />,
    label: "Refer a Friend",
    href: "#FAQs",
    number: 0,
    component: "Refer a Friend",
    isClose: false,
  },
  {
    icon: <HelpCircle size={28} strokeWidth={1.7} />,
    label: "Help",
    href: "#FAQs",
    number: 0,
    component: "Help",
    isClose: false,
  },
  {
    icon: <File size={28} strokeWidth={1.7} />,
    label: "About AMUZN",
    href: "#",
    number: 0,
    component: "About",
    isClose: false,
  },
  {
    icon: <Book size={28} strokeWidth={1.7} />,
    label: "T&C",
    href: "#FAQs",
    number: 0,
    component: "T&C",
    isClose: false,
  },
  {
    icon: <Settings size={28} strokeWidth={1.7} />,
    label: "Settings",
    href: "#FAQs",
    number: 0,
    component: "Settings",
    isClose: false,
  },
];

const navItemsNotLogin: NavItem[] = [
  {
    icon: <Home size={28} strokeWidth={1.7} />,
    label: "Home",
    href: "/",
    number: 0,
    component: "Home",
    isClose: true,
  },
  {
    icon: <UserPlus size={28} strokeWidth={1.7} />,
    label: "Refer a Friend",
    href: "#FAQs",
    number: 0,
    component: "Refer a Friend",
    isClose: false,
  },
  {
    icon: <HelpCircle size={28} strokeWidth={1.7} />,
    label: "Help",
    href: "#FAQs",
    number: 0,
    component: "Help",
    isClose: false,
  },
  {
    icon: <File size={28} strokeWidth={1.7} />,
    label: "About AMUZN",
    href: "#",
    number: 0,
    component: "About",
    isClose: false,
  },
  {
    icon: <Book size={28} strokeWidth={1.7} />,
    label: "T&C",
    href: "#FAQs",
    number: 0,
    component: "T&C",
    isClose: false,
  },
];

interface ApiField {
  name: string;
  type: "text" | "email" | "number" | "select" | "textarea";
  required: boolean;
  placeholder?: string;
  options?: string[];
}

interface ApiEndpoint {
  name: string;
  endpoint: string;
  method: string;
  description: string;
  fields: ApiField[];
}

interface ApiResponse {
  success: boolean;
  error?: string;
  [key: string]: any;
}

export function Sidebar({ open, onOpenChange }: any) {
  const user = useAuth();
  const { disconnect } = useDisconnect();
  const { address, isConnected } = useAccount();
  const [isOpen, setIsOpen] = useState(false);
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [activeSheet, setActiveSheet] = useState<string | null>("Home");
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const sidebarParam = searchParams.get("sidebar");
  const pathSegments = pathname ? pathname.split("/").filter(Boolean) : [];

  // State for adjacent drawers
  const [isBasketDrawerOpen, setIsBasketDrawerOpen] = useState(false);
  const [isOrdersDrawerOpen, setIsOrdersDrawerOpen] = useState(false);
  const [isNotificationsDrawerOpen, setIsNotificationsDrawerOpen] = useState(false);
  const [isChatsDrawerOpen, setIsChatsDrawerOpen] = useState(false);
  const [isReferDrawerOpen, setIsReferDrawerOpen] = useState(false);
  const [isHelpDrawerOpen, setIsHelpDrawerOpen] = useState(false);
  const [isAboutDrawerOpen, setIsAboutDrawerOpen] = useState(false);
  const [isTncDrawerOpen, setIsTncDrawerOpen] = useState(false);

  // State for nested drawers
  const [isConfirmPaymentOpen, setIsConfirmPaymentOpen] = useState(false);
  const [isOrderDetailOpen, setIsOrderDetailOpen] = useState(false);
  const [isChatBoxOpen, setIsChatBoxOpen] = useState(false);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [selectedChatName, setSelectedChatName] = useState<string | null>(null);
  const [selectedChatImg, setSelectedChatImg] = useState<string | null>(null);

  // State for T&C sub-sections
  const [isPrivacyPolicyOpen, setIsPrivacyPolicyOpen] = useState(false);
  const [isSecurityOpen, setIsSecurityOpen] = useState(false);
  const [isPaymentTermsOpen, setIsPaymentTermsOpen] = useState(false);

  // State for Refer a Friend share modal
  const [isReferShareModalOpen, setIsReferShareModalOpen] = useState(false);
  const [isStripeAccountModalOpen, setIsStripeAccountModalOpen] = useState(false);

  const [isCopied, setIsCopied] = useState(false);
  const [canUseNativeShare, setCanUseNativeShare] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const [selectedChatData, setSelectedChatData] = useState<{
    chatId: string;
    userName: string;
    imgUrl: string;
    chat: {
      fromProfile: string;
      toProfile: string;
      fromProfileName: string;
      toProfileName: string;
    };
    messages: any[];
  } | null>(null);

  // Add state for sidebar counts
  const [sidebarCounts, setSidebarCounts] = useState({
    basketCount: 0,
    myOrdersCount: 0,
    chatCount: 0,
    notificationCount: 0,
  });
  const [isLoadingCounts, setIsLoadingCounts] = useState(false);

  const handleItemClick = (component: string | null, isClose: boolean) => {
    // Always set the active sheet to the current component first
    setActiveSheet(component);

    // Close any open adjacent drawers first
    setIsBasketDrawerOpen(false);
    setIsOrdersDrawerOpen(false);
    setIsNotificationsDrawerOpen(false);
    setIsChatsDrawerOpen(false);
    setIsReferDrawerOpen(false);
    setIsHelpDrawerOpen(false);
    setIsAboutDrawerOpen(false);
    setIsTncDrawerOpen(false);

    // Close nested drawers
    setIsConfirmPaymentOpen(false);
    setIsOrderDetailOpen(false);
    setIsChatBoxOpen(false);
    setIsPrivacyPolicyOpen(false);
    setIsSecurityOpen(false);
    setIsPaymentTermsOpen(false);

    // Then handle specific actions for components
    if (component === "Basket") {
      setIsBasketDrawerOpen(true);
    } else if (component === "Orders") {
      setIsOrdersDrawerOpen(true);
    } else if (component === "Notifications") {
      setIsNotificationsDrawerOpen(true);
    } else if (component === "ChatList") {
      setIsChatsDrawerOpen(true);
    } else if (component === "Refer a Friend") {
      setIsReferShareModalOpen(true);
      onOpenChange(false); // Close the sidebar
    } else if (component === "Stripe Account") {
      setIsStripeAccountModalOpen(true);
      // onOpenChange(false); // Do not close the sidebar
    } else if (component === "Help") {
      setIsHelpDrawerOpen(true);
    } else if (component === "About") {
      setIsAboutDrawerOpen(true);
    } else if (component === "T&C") {
      setIsTncDrawerOpen(true);
    } else if (isClose) {
      // If it's the Home item or any item that should close the sidebar
      onOpenChange(false); // Close the sidebar
    }
  };

  useEffect(() => {
    // Check if user is logged in
    if (user.isLogin !== undefined) {
      // User login state has been determined
    }
  }, [user]);

  useEffect(() => {
    const closeChat = () => {
      setActiveSheet("Home");
    };

    closeEvent.on("close", closeChat);

    return () => {
      closeEvent.off("close", closeChat);
    };
  }, []);

  // Reset active sheet when pathname changes (navigation occurs)
  useEffect(() => {
    // Close any open adjacent drawers
    setIsBasketDrawerOpen(false);
    setIsOrdersDrawerOpen(false);
    setIsNotificationsDrawerOpen(false);
    setIsChatsDrawerOpen(false);
    setIsReferDrawerOpen(false);
    setIsHelpDrawerOpen(false);
    setIsAboutDrawerOpen(false);
    setIsTncDrawerOpen(false);

    // Close nested drawers
    setIsConfirmPaymentOpen(false);
    setIsOrderDetailOpen(false);
    setIsChatBoxOpen(false);
    setIsPrivacyPolicyOpen(false);
    setIsSecurityOpen(false);
    setIsPaymentTermsOpen(false);

    // Reset to Home unless it's one of the special menu items
    if (
      activeSheet !== "About" &&
      activeSheet !== "Help" &&
      activeSheet !== "T&C" &&
      activeSheet !== "Refer a Friend"
    ) {
      setActiveSheet("Home");
    }
  }, [pathname]);

  const deleteFirebaseIndexedDB = () => {
    const firebaseDBs = [
      "firebaseLocalStorageDb",
      "firebase-auth-database",
      "firebase-firestore-database",
      "firebase-firestore",
    ];

    firebaseDBs.forEach((dbName) => {
      indexedDB.deleteDatabase(dbName);
    });
  };

  // Function to handle logout
  const handleLogout = () => {
    deleteFirebaseIndexedDB();
    localStorage.clear();
    window.location.reload();
    window.location.href = "/";
  };

  // Check for native share support and mobile device
  useEffect(() => {
    const checkMobileAndShare = () => {
      // Check if device is mobile
      const isMobileDevice =
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        ) || window.innerWidth <= 768;

      setIsMobile(isMobileDevice);

      // Only enable native share on mobile devices that support it
      if (typeof navigator !== "undefined" && "share" in navigator && isMobileDevice) {
        setCanUseNativeShare(true);
      }
    };

    checkMobileAndShare();

    // Also check on window resize
    window.addEventListener("resize", checkMobileAndShare);
    return () => window.removeEventListener("resize", checkMobileAndShare);
  }, []);

  // Function to copy referral link with fallback for mobile
  const copyReferralLink = async () => {
    const referralUrl = "https://www.amuzn.app/";

    try {
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(referralUrl);
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
        return;
      }

      // Fallback for older browsers or non-secure contexts
      const textArea = document.createElement("textarea");
      textArea.value = referralUrl;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);

      if (successful) {
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      } else {
        throw new Error("Copy command failed");
      }
    } catch (err) {
      console.error("Failed to copy link: ", err);

      // Final fallback - show the URL in an alert for manual copy
      if (isMobile) {
        alert(`Copy this link: ${referralUrl}`);
      } else {
        // For desktop, try to select the URL text
        const urlElement = document.querySelector("[data-url-text]");
        if (urlElement) {
          const range = document.createRange();
          range.selectNodeContents(urlElement);
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    }
  };

  // Function to trigger native mobile share
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Check out AMUZN!",
          text: "Hey! I would like to invite you to check the AMUZN. It is an awesome place where creative entrepreneurs meet thousands of users, which can order their services:",
          url: "https://www.amuzn.app/",
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    }
  };

  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   let url;
  //    _generateFileUrl(postFile).then((resp)=>{
  //     console.log(resp);
  //     url = resp;
  //     return resp;
  //    }).catch((err)=>{
  //     console.log({err});
  //    })

  //   //  return url;
  //   // let url;
  //   // (async()=>{
  //   //   url = await _generateFileUrl(postFile);
  //   //   console.log("in" , url);
  //   // })()
  //   // console.log("url",url);

  //   // return url;

  //   // const baseUrl = process.env.BASE_STORAGE_URL;
  //   // if (!baseUrl) return undefined;

  //   // if (!postFile) {
  //   //   return undefined;
  //   // }

  //   // // this is for handleing both dev/prod db urls
  //   // if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //   //   return postFile;
  //   // }

  //   // return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  // Function to generate initials from a name
  const getInitials = (name: string | undefined): string => {
    if (!name) return "";

    const nameParts = name.trim().split(/\s+/);
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };

  // Load sidebar counts
  useEffect(() => {
    // console.log("enter in useEffect");
    // console.log({ user });
    // console.log(user?.users?.stripe_id);

    const loadSidebarCounts = async () => {
      if (!user.isLogin) {
        setSidebarCounts({
          basketCount: 0,
          myOrdersCount: 0,
          chatCount: 0,
          notificationCount: 0,
        });
        return;
      }

      setIsLoadingCounts(true);
      try {
        const resp = await GetSidebarCount();
        // console.log({ resp });
        setSidebarCounts({
          basketCount: resp.basketCount || 0,
          myOrdersCount: resp.myOrdersCount || 0,
          chatCount: resp.chatCount || 0,
          notificationCount: resp.notificationCount || 0,
        });
      } catch (error) {
        console.error("Error loading sidebar counts:", error);
        setSidebarCounts({
          basketCount: 0,
          myOrdersCount: 0,
          chatCount: 0,
          notificationCount: 0,
        });
      } finally {
        setIsLoadingCounts(false);
      }
    };

    loadSidebarCounts();
  }, [user.isLogin, user.userId, open]);

  // Update navItems with dynamic counts
  const navItemsWithCounts = navItems.map((item) => {
    if (item.component === "Basket") {
      return { ...item, number: sidebarCounts.basketCount };
    } else if (item.component === "Orders") {
      return { ...item, number: sidebarCounts.myOrdersCount };
    } else if (item.component === "Notifications") {
      return { ...item, number: sidebarCounts.notificationCount };
    } else if (item.component === "ChatList") {
      return { ...item, number: sidebarCounts.chatCount };
    }
    return item;
  });

  // Open the correct drawer when sidebar param changes
  useEffect(() => {
    if (!open) return;
    if (sidebarParam === "chat") {
      setIsChatsDrawerOpen(true);
      setActiveSheet("ChatList");
    } else if (sidebarParam === "basket") {
      setIsBasketDrawerOpen(true);
      setActiveSheet("Basket");
    } else if (sidebarParam === "orders") {
      setIsOrdersDrawerOpen(true);
      setActiveSheet("Orders");
    } else if (sidebarParam === "notifications") {
      setIsNotificationsDrawerOpen(true);

      setActiveSheet("Notifications");
    }
    // Add more as needed
  }, [sidebarParam, open]);

  // Remove sidebar param from URL when all drawers are closed
  useEffect(() => {
    if (
      !isChatsDrawerOpen &&
      !isBasketDrawerOpen &&
      !isOrdersDrawerOpen &&
      !isNotificationsDrawerOpen
    ) {
      const url = new URL(window.location.href);
      url.searchParams.delete("sidebar");
      router.replace(url.pathname + url.search, { scroll: false });
    } else {
      // reset notif count once draw is open
      // ofr better UX
      if (isNotificationsDrawerOpen) {
        setSidebarCounts({
          basketCount: sidebarCounts.basketCount,
          myOrdersCount: sidebarCounts.myOrdersCount,
          chatCount: sidebarCounts.chatCount,
          notificationCount: 0,
        });
      }
    }
  }, [isChatsDrawerOpen, isBasketDrawerOpen, isOrdersDrawerOpen, isNotificationsDrawerOpen]);

  // for stripe integration

  const [loading, setLoading] = useState<string | null>(null);
  const [results, setResults] = useState<Record<string, ApiResponse>>({});

  const stripeCreate = async (apiEndpoint: ApiEndpoint) => {
    setLoading(apiEndpoint.endpoint);
    try {
      // console.log("Making request to:", apiEndpoint.endpoint, "with userId:", user.userId);

      const response = await fetch(apiEndpoint.endpoint, {
        method: apiEndpoint.method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: user.userId,
        }),
      });

      // console.log("Response status:", response.status);
      // console.log("Response headers:", Object.fromEntries(response.headers.entries()));

      // Check if the response is ok (status 200-299)
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response body:", errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      // Check if the response is actually JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const responseText = await response.text();
        console.error("Non-JSON response:", responseText);
        throw new Error("Response is not JSON");
      }

      const responseData = await response.json();
      // console.log("Success response:", responseData);
      setResults((prev) => ({ ...prev, [apiEndpoint.endpoint]: responseData }));
    } catch (error) {
      console.error("Stripe API error:", error);
      setResults((prev) => ({
        ...prev,
        [apiEndpoint.endpoint]: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        },
      }));
    } finally {
      setLoading(null);
    }
  };
  const stripeAccountApiEndpoint = {
    name: "Connect Onboard",
    endpoint: "/api/connect/onboard",
    method: "POST",
    description: "Create Express account and onboarding link",
    fields: [],
  };
  return (
    <div>
      {/* Sidebar Navigation */}
      <Drawer
        isOpen={open}
        onOpenChange={onOpenChange}
        placement="left"
        size="md"
        radius="none"
        hideCloseButton={false}
        classNames={{
          closeButton: "drawer-close-button",
        }}
      >
        <DrawerContent className="w-[22rem]">
          {() => (
            <>
              <DrawerHeader className="px-4 pt-4 my-1">
                <div>
                  {pathSegments[0] === "profile" &&
                  pathSegments[1] === "lens" &&
                  (address || isConnected) ? (
                    <Link
                      href={`/profile/lens/${(user?.lensData?.username as any)?.localName ? (user?.lensData?.username as any)?.localName : ""}`}
                      className="flex flex-row items-center gap-4  cursor-pointer"
                      onClick={() => onOpenChange(false)}
                    >
                      {user?.lensData?.metadata &&
                      typeof user?.lensData?.metadata === "object" &&
                      (user?.lensData?.metadata as any)?.picture ? (
                        <img
                          src={(user?.lensData?.metadata as any)?.picture}
                          alt="Profile"
                          className="w-[50px] h-[50px] rounded-full"
                        />
                      ) : (
                        <div className="w-[50px] h-[50px] rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-xl">
                          {getInitials(
                            user?.lensData?.metadata && typeof user?.lensData?.metadata === "object"
                              ? (user?.lensData?.metadata as any)?.name
                              : "Profile Name"
                          )}
                        </div>
                      )}
                      <p className="text-xl font-medium">
                        {user?.lensData?.metadata && typeof user?.lensData?.metadata === "object"
                          ? (user?.lensData?.metadata as any)?.name
                          : "Profile Name"}
                      </p>
                    </Link>
                  ) : (
                    pathSegments[0] === "profile" &&
                    user.isLogin && (
                      <Link
                        href={`/profile/amuzn/${user?.userData?.profile_name?.replace(/\s+/g, "-")}`}
                        className="flex flex-row items-center gap-4  cursor-pointer"
                        onClick={() => onOpenChange(false)}
                      >
                        {generateFileUrl(user?.userData?.avatar) ? (
                          <img
                            src={generateFileUrl(user?.userData?.avatar)}
                            alt="Profile"
                            className="w-[50px] h-[50px] rounded-full"
                          />
                        ) : (
                          <div className="w-[50px] h-[50px] rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-xl">
                            {getInitials(user?.userData?.profile_name || "Profile Name")}
                          </div>
                        )}
                        <p className="text-xl font-medium">
                          {user?.userData?.profile_name || "Profile Name"}
                        </p>
                      </Link>
                    )
                  )}

                  {user.isLogin && pathSegments[0] !== "profile" ? (
                    <Link
                      href={`/profile/amuzn//${user?.userData?.profile_name?.replace(/\s+/g, "-")}`}
                      className="flex flex-row items-center gap-4  cursor-pointer"
                      onClick={() => onOpenChange(false)}
                    >
                      {generateFileUrl(user?.userData?.avatar) ? (
                        <img
                          src={generateFileUrl(user?.userData?.avatar)}
                          alt="Profile"
                          className="w-[50px] h-[50px] rounded-full"
                        />
                      ) : (
                        <div className="w-[50px] h-[50px] rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-xl">
                          {getInitials(user?.userData?.profile_name || "Profile Name")}
                        </div>
                      )}
                      <p className="text-xl font-medium">
                        {user?.userData?.profile_name || "Profile Name"}
                      </p>
                    </Link>
                  ) : (
                    (address || isConnected) &&
                    pathSegments[0] !== "profile" && (
                      <Link
                        href={`/profile/lens/${(user?.lensData?.username as any)?.localName ? (user?.lensData?.username as any)?.localName : ""}`}
                        className="flex flex-row items-center gap-4  cursor-pointer"
                        onClick={() => onOpenChange(false)}
                      >
                        {user?.lensData?.metadata &&
                        typeof user?.lensData?.metadata === "object" &&
                        (user?.lensData?.metadata as any)?.picture ? (
                          <img
                            src={(user?.lensData?.metadata as any)?.picture}
                            alt="Profile"
                            className="w-[50px] h-[50px] rounded-full"
                          />
                        ) : (
                          <div className="w-[50px] h-[50px] rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-xl">
                            {getInitials(
                              user?.lensData?.metadata &&
                                typeof user?.lensData?.metadata === "object"
                                ? (user?.lensData?.metadata as any)?.name
                                : "Profile Name"
                            )}
                          </div>
                        )}
                        <p className="text-xl font-medium">
                          {user?.lensData?.metadata && typeof user?.lensData?.metadata === "object"
                            ? (user?.lensData?.metadata as any)?.name
                            : "Profile Name"}{" "}
                        </p>
                      </Link>
                    )
                  )}
                </div>
              </DrawerHeader>
              <DrawerBody className="h-[80vh] overflow-y-auto gap-0 chat-scroll-custom pb-12 max-md:pb-16 px-4">
                {/* Always show navigation items */}
                {(user.isLogin ? navItemsWithCounts : navItemsNotLogin).map((item, index) => (
                  <Link
                    href={item.href}
                    key={index}
                    className={`group cursor-pointer flex flex-row items-center justify-between py-3 px-2 rounded-md transition-colors duration-200 hover:bg-gray-100`}
                    onClick={(e) => {
                      if (item.onClick?.()) {
                        e.preventDefault();
                        item.onClick();
                      } else if (item.isClose) {
                        setActiveSheet("Home");
                        onOpenChange(false);
                      } else {
                        e.preventDefault();
                        handleItemClick(item.component, item.isClose);
                      }
                    }}
                  >
                    <div className="flex flex-row items-center gap-4 h-8">
                      <div
                        className={`w-[3px] h-8 rounded-md transition-colors duration-200
                        ${
                          activeSheet === item.component
                            ? "bg-primary"
                            : "bg-white group-hover:bg-gray-300"
                        }
                        `}
                      ></div>
                      <div
                        className={
                          activeSheet === item.component
                            ? "text-primary flex items-center justify-center w-8 h-8"
                            : "text-[#BDBDBD] group-hover:text-gray-600 flex items-center justify-center w-8 h-8"
                        }
                      >
                        {React.cloneElement(item.icon as React.ReactElement, {
                          strokeWidth: activeSheet === item.component ? 2 : 1.7,
                        })}
                      </div>
                      <p
                        className={`text-lg flex items-center ${
                          activeSheet === item.component
                            ? "text-primary font-bold"
                            : "text-primary group-hover:text-gray-800"
                        }`}
                      >
                        {item.label}
                      </p>
                    </div>
                    {!(item.number == 0) && (
                      <Badge className="rounded-full bg-[#333333] py-[2px] px-2 text-center text-[14px] font-bold text-white">
                        {item.number}
                      </Badge>
                    )}
                  </Link>
                ))}
              </DrawerBody>
              <DrawerFooter>
                <div className="flex flex-col items-center justify-center gap-3">
                  {!address && !isConnected ? (
                    <Button
                      className="rounded-full w-full border-borderColor border-2 text-base text-black py-2 transition-colors duration-200 hover:bg-gray-100"
                      style={{ paddingTop: "10px" }}
                      variant="outline"
                      onClick={() => {
                        onOpenChange(false);
                        useSignInStore.getState().setIsOpen(true);
                      }}
                    >
                      <img
                        src="/assets/wallet.svg"
                        alt=""
                        height={20}
                        className="items-center h-[20px]"
                      />{" "}
                      <span>Connect Lens Account</span>
                    </Button>
                  ) : !user.isLogin ? (
                    <Button
                      variant="outline"
                      className="rounded-full w-full border-primary btn hover:bg-primary hover:text-white transition-colors duration-200"
                      onClick={() => {
                        onOpenChange(false), setIsSigninOpen(true);
                      }}
                    >
                      Sign In
                    </Button>
                  ) : (
                    ""
                  )}

                  {user.isLogin || address || isConnected ? (
                    <div
                      className="group pl-5  flex flex-row items-center gap-3 cursor-pointer bg-white w-[19rem] py-2 px-2 rounded-md transition-colors duration-200 hover:bg-gray-100"
                      onClick={() => {
                        onOpenChange(false), setIsOpen(true);
                      }}
                    >
                      <div className="text-[#BDBDBD] group-hover:text-gray-600 flex items-center justify-center w-8 h-8">
                        <Power size={28} strokeWidth={1.7} />
                      </div>
                      <div className="grid grid-cols-2">
                        <p className="group-hover:text-gray-800 flex text-lg items-center">
                          Sign Out
                        </p>
                        {address || isConnected ? (
                          <div className="flex items-center gap-1.5">
                            <span className="relative flex h-2 w-2">
                              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                              <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                            </span>
                            <p className="text-xs">Wallet Connected</p>
                          </div>
                        ) : (
                          ""
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="w-[19rem]">
                      <Button
                        variant="outline"
                        className="rounded-full w-full border-primary btn hover:bg-primary hover:text-white transition-colors duration-200"
                        onClick={() => {
                          onOpenChange(false), setIsSigninOpen(true);
                        }}
                      >
                        Sign In
                      </Button>
                    </div>
                  )}
                </div>
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>

      {/* Signout Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={setIsOpen}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content w-80 p-12 rounded-3xl">
            {() => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    Are you sure you want to Sign Out?
                  </p>
                  <div>
                    <Button
                      variant="outline"
                      className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                      onClick={() => {
                        handleLogout();
                        setIsOpen(false);
                        disconnect();
                      }}
                    >
                      Yes, sign out
                    </Button>
                    <Button
                      variant="outline"
                      className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                      onClick={() => setIsOpen(false)}
                    >
                      No, cancel
                    </Button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* signin/Signout Modal */}
      <div className="max-md:h-full">
        <Modal
          isDismissable={false}
          isOpen={isSigninOpen}
          placement="auto"
          onOpenChange={setIsSigninOpen}
          size="lg"
          classNames={{
            closeButton: "drawer-close-button",
          }}
        >
          <ModalContent className="modal-content py-10 px-28 max-md:px-8  md:max-h-none md:overflow-visible max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll hide-scroll">
            {() => (
              <>
                <ModalBody className="md:overflow-visible md:max-h-none max-md:overflow-scroll h-full hide-scroll">
                  <AuthSignup onClose={() => setIsSigninOpen(false)} />
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Adjacent Drawers */}
      <AdjacentDrawer
        isOpen={isBasketDrawerOpen}
        onOpenChange={setIsBasketDrawerOpen}
        title="Basket"
      >
        <Basket
          items={[1, 2, 3]}
          userId={user?.userId}
          onOpenChange={onOpenChange}
          onDrawerChange={setIsBasketDrawerOpen}
        />
      </AdjacentDrawer>

      <AdjacentDrawer
        isOpen={isOrdersDrawerOpen}
        onOpenChange={setIsOrdersDrawerOpen}
        title="My Orders"
      >
        <Orders
          userId={user?.userId}
          // userId="YHcuMxBfl1R7J562fUFwIghBqQw2"
          onOpenChange={onOpenChange}
          onDrawerChange={setIsOrdersDrawerOpen}
        />
      </AdjacentDrawer>

      <AdjacentDrawer
        isOpen={isNotificationsDrawerOpen}
        onOpenChange={setIsNotificationsDrawerOpen}
        title="Notifications"
      >
        <div className="flex flex-col mr-4">
          <Notifications
            onOpenChange={onOpenChange}
            onDrawerChange={setIsNotificationsDrawerOpen}
          />
        </div>
      </AdjacentDrawer>

      <AdjacentDrawer isOpen={isChatsDrawerOpen} onOpenChange={setIsChatsDrawerOpen} title="Chats">
        <ChatList
          onSelectChat={(chatData) => {
            setSelectedChatData(chatData);
            setSelectedChatId(chatData.chatId);
            setSelectedChatName(chatData.userName);
            setSelectedChatImg(chatData.imgUrl);
            setIsChatBoxOpen(true);
          }}
        />
      </AdjacentDrawer>

      {/* Nested ChatBox Drawer */}
      <NestedDrawer
        isOpen={isChatBoxOpen}
        onOpenChange={setIsChatBoxOpen}
        title={selectedChatName || "Chat"}
      >
        {selectedChatData && (
          <ChatBox
            selectedChatId={selectedChatData.chatId}
            selectedChatName={selectedChatData.userName}
            selectedChatImg={selectedChatData.imgUrl}
            userProfileName={user?.userData?.profile_name}
            onBack={() => setIsChatBoxOpen(false)}
            chatData={selectedChatData}
          />
        )}
      </NestedDrawer>

      {/* NEW: Info Drawers for Refer a Friend, Help, About AMUZN, and T&C */}
      <AdjacentDrawer
        isOpen={isReferDrawerOpen}
        onOpenChange={setIsReferDrawerOpen}
        title="Refer a Friend"
      >
        <div className="flex flex-col mr-4">
          <ReferNotion />
        </div>
      </AdjacentDrawer>

      <AdjacentDrawer isOpen={isHelpDrawerOpen} onOpenChange={setIsHelpDrawerOpen} title="Help">
        <div className="flex flex-col mr-4">
          <HelpNotion />
        </div>
      </AdjacentDrawer>

      <AdjacentDrawer
        isOpen={isAboutDrawerOpen}
        onOpenChange={setIsAboutDrawerOpen}
        title="About AMUZN"
      >
        <div className="flex flex-col mr-4">
          <AboutNotion />
        </div>
      </AdjacentDrawer>

      <AdjacentDrawer
        isOpen={isTncDrawerOpen}
        onOpenChange={setIsTncDrawerOpen}
        title="Terms & Conditions"
      >
        <div className="flex flex-col mr-4">
          {/* T&C Content here */}
          <div className="mb-6">
            {
              // here  you can write the tnc content
              <RichTextFormatter
                text={tncdata}
                className="mt-2 text-subtitle"
                preserveWhitespace={true}
                enableMarkdown={true}
              />
            }
          </div>

          {/* Three buttons for nested drawers */}
          <div className="pt-4 space-y-3">
            <button
              className={`w-full text-left p-4 border border-gray-200 rounded-lg transition-colors duration-200 ${isPrivacyPolicyOpen ? "bg-blue-50 border-blue-200 text-blue-900" : "hover:bg-gray-50"}`}
              onClick={() => setIsPrivacyPolicyOpen(true)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-primary">Privacy Policy</h4>
                </div>
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </button>

            <button
              className={`w-full text-left p-4 border border-gray-200 rounded-lg transition-colors duration-200 ${isSecurityOpen ? "bg-blue-50 border-blue-200 text-blue-900" : "hover:bg-gray-50"}`}
              onClick={() => setIsSecurityOpen(true)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-primary">Terms of Service</h4>
                </div>
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </button>

            <button
              className={`w-full text-left p-4 border border-gray-200 rounded-lg transition-colors duration-200 ${isPaymentTermsOpen ? "bg-blue-50 border-blue-200 text-blue-900" : "hover:bg-gray-50"}`}
              onClick={() => setIsPaymentTermsOpen(true)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-primary">Payment Terms</h4>
                </div>
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </button>
          </div>
        </div>
      </AdjacentDrawer>

      {/* NEW: Nested Drawers for T&C Sub-sections */}
      <NestedDrawer
        isOpen={isPrivacyPolicyOpen}
        onOpenChange={setIsPrivacyPolicyOpen}
        title="Privacy Policy"
        size="lg"
      >
        <div className="flex flex-col mr-4">
          <PrivacyPolicyNotion />
        </div>
      </NestedDrawer>

      <NestedDrawer
        isOpen={isSecurityOpen}
        onOpenChange={setIsSecurityOpen}
        title="Terms of Service"
      >
        <div className="flex flex-col mr-4">
          <TermsOfServicesNotion />
        </div>
      </NestedDrawer>

      <NestedDrawer
        isOpen={isPaymentTermsOpen}
        onOpenChange={setIsPaymentTermsOpen}
        title="Payment Terms"
        size="lg"
      >
        <div className="flex flex-col mr-4">
          <PaymentTermsNotion />
        </div>
      </NestedDrawer>

      {/* Refer a Friend Share Modal */}
      <Modal
        isDismissable={false}
        isOpen={isReferShareModalOpen}
        placement="auto"
        onOpenChange={setIsReferShareModalOpen}
        hideCloseButton={true}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent className="modal-content max-h-[90vh]">
          {() => (
            <>
              <ModalBody className="p-0">
                <div className="relative">
                  {/* Header with gradient background */}
                  <div className="bg-gradient-to-r from-primary to-primary/80 text-white p-6 rounded-t-lg">
                    <div className="flex items-center justify-between">
                      <div
                        onClick={() => setIsReferShareModalOpen(false)}
                        className="p-2 rounded-full hover:bg-white/20 cursor-pointer transition-colors"
                      >
                        <X size={20} className="text-white" />
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="bg-white/20 p-2 rounded-full">
                          <UserPlus size={24} className="text-white" />
                        </div>
                        <h3 className="font-bold text-xl">Refer a Friend</h3>
                      </div>
                      <div className="w-10"></div>
                    </div>

                    {/* Hero section */}
                    <div className="text-center mt-6">
                      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                        <div className="flex justify-center mb-4">
                          <div className="bg-white/20 p-4 rounded-full">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="32"
                              height="32"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-white"
                            >
                              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                              <circle cx="9" cy="7" r="4"></circle>
                              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                            </svg>
                          </div>
                        </div>
                        <h4 className="text-lg font-semibold mb-2">Share AMUZN with Friends</h4>
                        <p className="text-white/90 text-sm leading-relaxed">
                          Invite your friends to discover amazing creative services and connect with
                          talented entrepreneurs
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Content section */}
                  <div className="p-6">
                    {/* Message preview card */}
                    <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 mb-6">
                      <div className="flex items-start gap-4">
                        <div className="bg-primary/10 p-3 rounded-full flex-shrink-0">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-primary"
                          >
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                          </svg>
                        </div>
                        <div className="flex-1">
                          <h5 className="font-semibold text-gray-800 mb-2">
                            Your referral message:
                          </h5>
                          <div className="bg-white rounded-lg p-4 border border-gray-100 shadow-sm">
                            <p className="text-gray-700 text-sm leading-relaxed">
                              <span className="font-medium">Hey!</span>
                              <br />I would like to invite you to check the AMUZN. It is an awesome
                              place where creative entrepreneurs meet thousands of users, which can
                              order their services:
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Native Share Button (Mobile Only) */}
                    {canUseNativeShare && isMobile && (
                      <div className="mb-6">
                        <button
                          onClick={handleNativeShare}
                          className="w-full bg-gradient-to-r from-primary to-primary/80 text-white p-4 rounded-xl font-semibold text-lg hover:from-primary/90 hover:to-primary/70 transition-all duration-200 hover:shadow-lg flex items-center justify-center gap-3"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-white"
                          >
                            <circle cx="18" cy="5" r="3"></circle>
                            <circle cx="6" cy="12" r="3"></circle>
                            <circle cx="18" cy="19" r="3"></circle>
                            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                          </svg>
                          Share via Mobile Apps
                        </button>
                      </div>
                    )}

                    {/* Quick copy section */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 mb-6">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-blue-600"
                          >
                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                          </svg>
                        </div>
                        <h5 className="font-semibold text-gray-800">Quick Share Link</h5>
                      </div>
                      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                        <div className="flex items-center max-md:flex-col max-md:items-start justify-between gap-3">
                          <div className="flex-1 min-w-0">
                            <p className="text-xs text-gray-500 mb-1">
                              {isMobile
                                ? "Tap to copy or long-press to select:"
                                : "Share this link:"}
                            </p>
                            <p
                              className="text-sm text-gray-700 font-mono truncate cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                              data-url-text
                              onClick={isMobile ? copyReferralLink : undefined}
                              onTouchStart={
                                isMobile
                                  ? (e) => {
                                      // Add visual feedback on touch
                                      e.currentTarget.style.backgroundColor = "#f3f4f6";
                                    }
                                  : undefined
                              }
                              onTouchEnd={
                                isMobile
                                  ? (e) => {
                                      // Remove visual feedback
                                      setTimeout(() => {
                                        e.currentTarget.style.backgroundColor = "";
                                      }, 150);
                                    }
                                  : undefined
                              }
                            >
                              https://www.amuzn.app/
                            </p>
                          </div>
                          <button
                            onClick={copyReferralLink}
                            className={`flex items-center gap-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 touch-manipulation ${
                              isCopied
                                ? "bg-green-100 text-green-700 border border-green-200"
                                : "bg-primary text-white hover:bg-primary/90 hover:shadow-md active:bg-primary/80"
                            }`}
                            style={{ minHeight: "44px" }} // Ensure minimum touch target size
                          >
                            {isCopied ? (
                              <>
                                <Check size={16} />
                                <span className="text-sm">Copied!</span>
                              </>
                            ) : (
                              <>
                                <Copy size={16} />
                                <span className="text-sm">Copy</span>
                              </>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Social Media Section */}
                    <div className="bg-white rounded-xl border border-gray-200 p-6 mb-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="bg-gradient-to-r from-pink-100 to-purple-100 p-2 rounded-full">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-purple-600"
                          >
                            <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                          </svg>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-800">
                          Share on Social Media
                        </h4>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
                        {/* WhatsApp */}
                        <a
                          href={`https://wa.me/?text=${encodeURIComponent(
                            "Hey!\nI would like to invite you to check the AMUZN. It is an awesome place where creative entrepreneurs meet thousands of users, which can order their services:\n\nhttps://www.amuzn.app/"
                          )}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group"
                        >
                          <div className="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border border-green-200 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-green-500 p-3 rounded-full group-hover:bg-green-600 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                WhatsApp
                              </span>
                            </div>
                          </div>
                        </a>

                        {/* Facebook */}
                        <a
                          href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                            "https://www.amuzn.app/"
                          )}&quote=${encodeURIComponent(
                            "Hey! I would like to invite you to check the AMUZN. It is an awesome place where creative entrepreneurs meet thousands of users, which can order their services:"
                          )}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group"
                        >
                          <div className="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-200 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-blue-600 p-3 rounded-full group-hover:bg-blue-700 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                Facebook
                              </span>
                            </div>
                          </div>
                        </a>

                        {/* Twitter */}
                        <a
                          href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(
                            "Hey! I would like to invite you to check the AMUZN. It is an awesome place where creative entrepreneurs meet thousands of users, which can order their services:"
                          )}&url=${encodeURIComponent("https://www.amuzn.app/")}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group"
                        >
                          <div className="bg-gradient-to-br from-sky-50 to-sky-100 hover:from-sky-100 hover:to-sky-200 border border-sky-200 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-sky-500 p-3 rounded-full group-hover:bg-sky-600 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                Twitter
                              </span>
                            </div>
                          </div>
                        </a>

                        {/* LinkedIn */}
                        <a
                          href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
                            "https://www.amuzn.app/"
                          )}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group"
                        >
                          <div className="bg-gradient-to-br from-blue-50 to-indigo-100 hover:from-blue-100 hover:to-indigo-200 border border-blue-300 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-blue-700 p-3 rounded-full group-hover:bg-blue-800 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                  <rect x="2" y="9" width="4" height="12"></rect>
                                  <circle cx="4" cy="4" r="2"></circle>
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                LinkedIn
                              </span>
                            </div>
                          </div>
                        </a>

                        {/* Instagram */}
                        <a
                          href={`https://www.instagram.com/`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group"
                          onClick={(e) => {
                            e.preventDefault();
                            // Instagram doesn't have a direct share URL, so we copy the link and open Instagram
                            copyReferralLink();
                            window.open("https://www.instagram.com/", "_blank");
                          }}
                        >
                          <div className="bg-gradient-to-br from-pink-50 to-purple-100 hover:from-pink-100 hover:to-purple-200 border border-pink-200 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-gradient-to-r from-pink-500 to-purple-600 p-3 rounded-full group-hover:from-pink-600 group-hover:to-purple-700 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                Instagram
                              </span>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>

                    {/* Messaging Section */}
                    <div className="bg-white rounded-xl border border-gray-200 p-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="bg-gradient-to-r from-orange-100 to-red-100 p-2 rounded-full">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-orange-600"
                          >
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                          </svg>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-800">Share via Messaging</h4>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        {/* Telegram */}
                        <a
                          href={`https://telegram.me/share/url?url=${encodeURIComponent(
                            "https://www.amuzn.app/"
                          )}&text=${encodeURIComponent(
                            "Hey! I would like to invite you to check the AMUZN. It is an awesome place where creative entrepreneurs meet thousands of users, which can order their services:"
                          )}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group"
                        >
                          <div className="bg-gradient-to-br from-cyan-50 to-blue-100 hover:from-cyan-100 hover:to-blue-200 border border-cyan-200 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-cyan-500 p-3 rounded-full group-hover:bg-cyan-600 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <line x1="22" y1="2" x2="11" y2="13"></line>
                                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                Telegram
                              </span>
                            </div>
                          </div>
                        </a>

                        {/* Email */}
                        <a
                          href={`mailto:?subject=${encodeURIComponent(
                            "Check out AMUZN!"
                          )}&body=${encodeURIComponent(
                            "Hey!\n\nI would like to invite you to check the AMUZN. It is an awesome place where creative entrepreneurs meet thousands of users, which can order their services:\n\nhttps://www.amuzn.app/"
                          )}`}
                          className="group"
                        >
                          <div className="bg-gradient-to-br from-blue-50 to-indigo-100 hover:from-blue-100 hover:to-indigo-200 border border-blue-200 rounded-xl p-4 transition-all duration-200 hover:shadow-md hover:scale-105">
                            <div className="flex flex-col items-center gap-2">
                              <div className="bg-blue-600 p-3 rounded-full group-hover:bg-blue-700 transition-colors">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="text-white"
                                >
                                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"></path>
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                Email
                              </span>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>

                    {/* Footer with tips */}
                    <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-4 border border-amber-200 mt-6">
                      <div className="flex items-start gap-3">
                        <div className="bg-amber-100 p-2 rounded-full flex-shrink-0">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-amber-600"
                          >
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 16v-4"></path>
                            <path d="M12 8h.01"></path>
                          </svg>
                        </div>
                        <div>
                          <h6 className="font-medium text-amber-800 mb-1">Pro Tip</h6>
                          <p className="text-sm text-amber-700 leading-relaxed">
                            Share AMUZN with friends who are creative entrepreneurs or looking for
                            unique services. They'll love discovering talented creators!
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Stripe Account AdjacentDrawer - Updated with Connect Embedded Components */}
      <AdjacentDrawer
        isOpen={isStripeAccountModalOpen}
        onOpenChange={setIsStripeAccountModalOpen}
        title="Stripe"
      >
        <StripeAuthFlow />
      </AdjacentDrawer>
    </div>
  );
}

function ConnectSidebarContent() {
  const user = useAuth();
  const [sellerAccount, setSellerAccount] = useState<{
    stripeAccountId: string;
    onboardingComplete: boolean;
    chargesEnabled: boolean;
    payoutsEnabled: boolean;
    email?: string;
    businessName?: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch seller account information
  useEffect(() => {
    const fetchSellerAccount = async () => {
      if (!user?.userId) {
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/sellers/${user.userId}`);
        if (response.ok) {
          const data = await response.json();
          setSellerAccount(data);
        } else if (response.status === 404) {
          // No seller account exists yet
          setSellerAccount(null);
        } else {
          throw new Error("Failed to fetch seller account");
        }
      } catch (err) {
        console.error("Error fetching seller account:", err);
        setError(err instanceof Error ? err.message : "Failed to load account");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSellerAccount();
  }, [user?.userId]);

  const handleCreateAccount = async () => {
    if (!user?.userId || !user?.users?.email) {
      setError("User information is required");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/connect/onboard", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user.userId,
          email: user.users.email,
          businessName: user.userData?.profile_name || user.users.email,
        }),
      });

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      // Check if user already had an account
      if (data.existing) {
        console.log("User already has Stripe account:", data.accountId);
      }

      // Refresh seller account data
      const accountResponse = await fetch(`/api/sellers/${user.userId}`);
      if (accountResponse.ok) {
        const accountData = await accountResponse.json();
        setSellerAccount(accountData);
      } else {
        // If the API call fails, create a basic account object from the response
        setSellerAccount({
          stripeAccountId: data.accountId,
          onboardingComplete: false,
          chargesEnabled: false,
          payoutsEnabled: false,
          email: user.users.email,
          businessName: user.userData?.profile_name || user.users.email,
        });
      }
    } catch (err) {
      console.error("Error creating account:", err);
      setError(err instanceof Error ? err.message : "Failed to create account");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <svg
            className="animate-spin h-8 w-8 text-primary mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
          <p className="text-gray-600">Loading your seller dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
            <p className="text-sm text-red-700 mb-4">{error}</p>
            <button
              className="bg-red-600 text-white px-4 py-2 rounded font-medium hover:bg-red-700 transition-colors"
              onClick={() => window.location.reload()}
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // No seller account - show creation flow
  if (!sellerAccount) {
    return (
      <div className="p-6">
        <div className="text-center">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Become a Seller</h3>
            <p className="text-sm text-blue-700 mb-4">
              Start accepting payments by setting up your seller account with Stripe Connect.
            </p>
            <div className="text-xs text-blue-600 space-y-1">
              <p>✓ Embedded onboarding - no redirects</p>
              <p>✓ Secure payment processing</p>
              <p>✓ Real-time dashboard</p>
            </div>
          </div>

          <button
            onClick={handleCreateAccount}
            className="bg-primary text-white px-6 py-3 rounded-full font-medium transition-colors shadow-lg"
          >
            Create Seller Account
          </button>

          <div className="mt-4 space-y-2">
            <button
              onClick={() => window.open("/payment/connect-demo", "_blank")}
              className="text-primary hover:text-primary/80 text-sm underline block"
            >
              View Demo First
            </button>
            <button
              onClick={() => window.open("/payment/connect-dashboard", "_blank")}
              className="text-gray-600 hover:text-gray-800 text-sm underline block"
            >
              Open Full Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Seller account exists - show embedded dashboard
  return (
    <div className="h-full">
      <ConnectEmbeddedProvider
        accountId={sellerAccount.stripeAccountId}
        components={[
          "account_onboarding",
          "account_management",
          "payments",
          "payouts",
          "balances",
          "notification_banner",
        ]}
      >
        <div className="p-4">
          {/* Quick action header */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold text-blue-900 text-sm">Seller Dashboard</h4>
                <p className="text-xs text-blue-700">
                  {sellerAccount.onboardingComplete ? "Account Active" : "Setup Required"}
                </p>
              </div>
              <button
                onClick={() => window.open("/payment/connect-dashboard", "_blank")}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
              >
                Full Dashboard
              </button>
            </div>
          </div>

          <ConnectDashboard />
        </div>
      </ConnectEmbeddedProvider>
    </div>
  );
}

function StripeAccountContent({
  results,
  setResults,
  stripeAccountApiEndpoint,
  stripeCreate,
  loading,
}: {
  results: Record<string, ApiResponse>;
  setResults: React.Dispatch<React.SetStateAction<Record<string, ApiResponse>>>;
  stripeAccountApiEndpoint: ApiEndpoint;
  stripeCreate: (apiEndpoint: ApiEndpoint) => Promise<void>;
  loading: string | null;
}) {
  const connectResult = results[stripeAccountApiEndpoint.endpoint];
  const [stripeStatus, setStripeStatus] = useState<{
    loading: boolean;
    hasStripeId: boolean;
    stripeId?: string;
    accountDetails?: any;
    error?: string;
  }>({
    loading: true,
    hasStripeId: false,
  });

  // Check user's Stripe ID status on mount
  useEffect(() => {
    const checkStripeStatus = async () => {
      try {
        const userId = getAuthenticatedUserId();
        if (!userId) {
          setStripeStatus({
            loading: false,
            hasStripeId: false,
            error: "Please log in to check your Stripe account status",
          });
          return;
        }

        // First check if user has stripe_id
        const statusResponse = await fetch("/api/user/stripe-status", {
          method: "GET",
          headers: getAuthHeaders(),
        });

        if (!statusResponse.ok) {
          throw new Error("Failed to check Stripe status");
        }

        const statusData = await statusResponse.json();

        if (statusData.hasStripeId) {
          // If user has stripe_id, fetch full account details
          const accountResponse = await fetch("/api/connect/my-account", {
            method: "GET",
            headers: getAuthHeaders(),
          });

          if (accountResponse.ok) {
            const accountData = await accountResponse.json();
            setStripeStatus({
              loading: false,
              hasStripeId: true,
              stripeId: statusData.stripeId,
              accountDetails: accountData.account,
            });
          } else {
            // Has stripe_id but couldn't fetch details
            setStripeStatus({
              loading: false,
              hasStripeId: true,
              stripeId: statusData.stripeId,
              error: "Could not fetch account details",
            });
          }
        } else {
          // No stripe_id found
          setStripeStatus({
            loading: false,
            hasStripeId: false,
          });
        }
      } catch (error) {
        console.error("Error checking Stripe status:", error);
        setStripeStatus({
          loading: false,
          hasStripeId: false,
          error: "Failed to check Stripe account status",
        });
      }
    };

    checkStripeStatus();
  }, []);

  // Handle URL opening effect
  useEffect(() => {
    if (connectResult && connectResult.url) {
      window.open(connectResult.url, "self");
      // Remove the url from the result so it doesn't keep opening
      setResults((prev) => ({
        ...prev,
        [stripeAccountApiEndpoint.endpoint]: { ...connectResult, url: undefined },
      }));
    }
  }, [connectResult, setResults, stripeAccountApiEndpoint.endpoint]);

  // Handle localStorage storage effect
  useEffect(() => {
    if (connectResult && connectResult.accountId) {
      localStorage.setItem("stripeAccount", JSON.stringify(connectResult));
    }
  }, [connectResult]);

  // Show loading state while checking Stripe status
  if (stripeStatus.loading) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-8">
        <div className="flex items-center justify-center">
          <svg
            className="animate-spin h-8 w-8 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
          <span className="ml-2 text-gray-600">Checking Stripe account status...</span>
        </div>
      </div>
    );
  }

  // Show error if there was an issue checking status
  if (stripeStatus.error) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 w-full max-w-md text-center">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
          <p className="text-sm text-red-700">{stripeStatus.error}</p>
          <button
            className="mt-4 bg-red-600 text-white px-4 py-2 rounded font-medium hover:bg-red-700 transition-colors"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
        <button
          className="bg-primary text-white px-6 py-3 mt-6 rounded-full font-medium transition-colors shadow-lg flex items-center justify-center min-w-[220px]"
          onClick={() => stripeCreate(stripeAccountApiEndpoint)}
          disabled={loading === stripeAccountApiEndpoint.endpoint}
        >
          {loading === stripeAccountApiEndpoint.endpoint ? (
            <>
              <svg
                className="animate-spin h-5 w-5 mr-2 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
              </svg>
              Connecting...
            </>
          ) : (
            "Connect or Create Stripe Account"
          )}
        </button>
      </div>
    );
  }

  // If user has Stripe ID and account details, show account info
  if (stripeStatus.hasStripeId && stripeStatus.accountDetails) {
    return <StripeAccountDrawerContent accountDetails={stripeStatus.accountDetails} />;
  }

  // If user has Stripe ID but no details, show basic info
  if (stripeStatus.hasStripeId && !stripeStatus.accountDetails) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 w-full max-w-md text-center">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">Stripe Account Found</h3>
          <div className="text-sm text-blue-700 mb-2">
            Account ID: <span className="font-mono">{stripeStatus.stripeId}</span>
          </div>
          <p className="text-sm text-blue-600 mb-4">
            Your Stripe account is connected but we couldn't fetch the details.
          </p>
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded font-medium hover:bg-blue-700 transition-colors"
            onClick={() => window.location.reload()}
          >
            Refresh Details
          </button>
        </div>
      </div>
    );
  }

  // If connected via the current session, show account details
  if (connectResult && connectResult.accountId) {
    return <StripeAccountDrawerContent connectResult={connectResult} />;
  }

  // Not connected: show connect/create options
  return (
    <div className="w-full flex flex-col items-center justify-center py-8">
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 w-full max-w-md text-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Stripe Account Required</h3>
        <p className="text-sm text-gray-600 mb-4">
          You need a Stripe account to receive payments. You can either connect an existing account
          or create a new one.
        </p>
      </div>

      <button
        className="bg-primary text-white px-6 py-3 rounded-full font-medium transition-colors shadow-lg flex items-center justify-center min-w-[220px]"
        onClick={() => stripeCreate(stripeAccountApiEndpoint)}
        disabled={loading === stripeAccountApiEndpoint.endpoint}
      >
        {loading === stripeAccountApiEndpoint.endpoint ? (
          <>
            <svg
              className="animate-spin h-5 w-5 mr-2 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
            Connecting...
          </>
        ) : (
          "Connect or Create Stripe Account"
        )}
      </button>
      {connectResult && connectResult.error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-red-700 w-full max-w-md text-center">
          {connectResult.error}
        </div>
      )}
    </div>
  );
}

function StripeAccountDrawerContent({
  connectResult,
  accountDetails,
}: {
  connectResult?: any;
  accountDetails?: any;
}) {
  useEffect(() => {
    if (connectResult && connectResult.accountId) {
      // Store the Stripe account data in localStorage
      localStorage.setItem("stripeAccount", JSON.stringify(connectResult));
    }
  }, [connectResult]);

  console.log(accountDetails);

  // Handle account details from API
  if (accountDetails) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-8">
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 w-full max-w-md text-center">
          <h3 className="text-lg font-semibold text-green-800 mb-2">Stripe Account Connected</h3>
          <div className="text-sm text-green-700 mb-2">
            Account ID: <span className="font-mono">{accountDetails.id}</span>
          </div>
          {accountDetails.email && (
            <div className="text-sm text-green-700 mb-2">
              Email: <span className="font-mono">{accountDetails.email}</span>
            </div>
          )}
          <div className="text-sm text-green-700 mb-2">
            Country: <span className="font-mono">{accountDetails.country}</span>
          </div>
          <div className="text-sm text-green-700 mb-2">
            Currency:{" "}
            <span className="font-mono">{accountDetails.defaultCurrency?.toUpperCase()}</span>
          </div>

          {/* Account Status */}
          <div className="mt-4 p-3 bg-white rounded border border-gray-100">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">Account Status</h4>
            <div className="text-xs text-left space-y-1">
              <div>
                Charges:{" "}
                <span
                  className={
                    accountDetails.status?.chargesEnabled ? "text-green-600" : "text-red-600"
                  }
                >
                  {accountDetails.status?.chargesEnabled ? "Enabled" : "Disabled"}
                </span>
              </div>
              <div>
                Payouts:{" "}
                <span
                  className={
                    accountDetails.status?.payoutsEnabled ? "text-green-600" : "text-red-600"
                  }
                >
                  {accountDetails.status?.payoutsEnabled ? "Enabled" : "Disabled"}
                </span>
              </div>
              <div>
                Details Submitted:{" "}
                <span
                  className={
                    accountDetails.status?.detailsSubmitted ? "text-green-600" : "text-red-600"
                  }
                >
                  {accountDetails.status?.detailsSubmitted ? "Yes" : "No"}
                </span>
              </div>
            </div>
          </div>

          {/* Balance Information */}
          {accountDetails.balance && (
            <div className="mt-4 p-3 bg-white rounded border border-gray-100">
              <h4 className="text-sm font-semibold text-gray-800 mb-2">Balance</h4>
              <div className="text-xs text-left space-y-1">
                {accountDetails.balance.available?.map((bal: any, index: number) => (
                  <div key={index}>
                    Available ({bal.currency.toUpperCase()}): {(bal.amount / 100).toFixed(2)}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Handle connectResult from onboarding flow
  if (connectResult && connectResult.accountId) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-8">
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 w-full max-w-md text-center">
          <h3 className="text-lg font-semibold text-green-800 mb-2">Stripe Account Connected</h3>
          <div className="text-sm text-green-700 mb-2">
            Account ID: <span className="font-mono">{connectResult.accountId}</span>
          </div>
          {connectResult.details && (
            <pre className="text-xs text-left bg-white rounded p-2 border border-gray-100 mt-2 overflow-x-auto">
              {JSON.stringify(connectResult.details, null, 2)}
            </pre>
          )}
        </div>
      </div>
    );
  }

  return null;
}
