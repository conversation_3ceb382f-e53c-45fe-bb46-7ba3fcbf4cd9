"use client";
import MyProfile from "./myProfile";
import { useEffect, useRef, useState } from "react";
import useProfile from "@/hook/profileData";
import useAuth from "@/hook";
import MyLensProfile from "./lensProfile";
import { useRouter, useSearchParams } from "next/navigation";
import { getUserIdByProfileName } from "@/services/usersServices";
import { useAccountQuery } from "@/graphql/test/generated";

type SelectedCategoryProps = {
  userId: string;
  profileType?: "amuzn" | "lens";
  profile_name?: string;
};

const Profile = ({ userId, profileType, profile_name }: SelectedCategoryProps) => {
  const searchParams = useSearchParams();
  const view = searchParams?.get("view");
  const [otherUserStatus, setOtherUserStatus] = useState(false);
  const [fetchedUserId, setFetchedUserId] = useState<string | null>(null);
  const router = useRouter();

  const postsRef = useRef<HTMLDivElement>(null);
  const auth = useAuth();
  const { profileData } = useProfile(userId !== "my-profile" ? userId : auth.userId);

  // Lens profile query - only for lens profile type
  const { data: profilelensData } = useAccountQuery(
    {
      request: {
        username: {
          localName: profile_name ?? "",
        },
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: profileType === "lens" && !!profile_name,
    }
  );

  // Amuzn profile service call - only for amuzn profile type
  useEffect(() => {
    const fetchUserIdByProfileName = async () => {
      if (profileType === "amuzn" && profile_name) {
        try {
          const result = await getUserIdByProfileName(
            decodeURIComponent((profile_name as string)?.replace(/-/g, " "))
          );
          // console.log(`User ID for profile name "${profile_name}":`, result);
          setFetchedUserId(result);
        } catch (error) {
          console.error("Error fetching user ID by profile name:", error);
        }
      }
    };

    fetchUserIdByProfileName();
  }, [profile_name, profileType]);

  useEffect(() => {
    if (profileData?.categories?.[0]) {
      localStorage.setItem(
        "usercategorie",
        profileData?.categories?.[0] == "Storytelling" ? "Literature" : profileData?.categories?.[0]
      );
    }

    if (fetchedUserId) {
      if (fetchedUserId == auth.userId) {
        setOtherUserStatus(false);
      } else {
        setOtherUserStatus(true);
      }
    }
  }, [userId, auth, view, router, fetchedUserId]);

  return (
    <>
      <div className="relative w-full px-3 max-md:px-0 mt-3 md:overflow-hidden md:h-[calc(100vh-155px)] bg-[#fafafa] ">
        <div className="" ref={postsRef}>
          {profileType === "lens" ? (
            <MyLensProfile
              Category={
                profileData?.categories?.[0] == "Storytelling"
                  ? "Literature"
                  : profileData?.categories?.[0]
              }
              userId={profilelensData?.account?.address}
              otherUserStatus={otherUserStatus}
            />
          ) : (
            <MyProfile userId={fetchedUserId} otherUserStatus={otherUserStatus} />
          )}
        </div>
      </div>
    </>
  );
};

export default Profile;
