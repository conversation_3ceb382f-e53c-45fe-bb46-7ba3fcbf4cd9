import admin from "firebase-admin";
import { initAdmin } from "../../firebaseAdminConfig";

function stripUndefined<T extends Record<string, any>>(obj: T): T {
  const entries = Object.entries(obj).filter(([, v]) => v !== undefined);
  return Object.fromEntries(entries) as T;
}

// Types are intentionally light to keep this generic for different account types
export interface StripeAccountLike {
  id: string;
  email?: string | null;
  type?: string | null;
  country?: string | null;
  default_currency?: string | null;
  business_type?: string | null;
  details_submitted?: boolean | null;
  charges_enabled?: boolean | null;
  payouts_enabled?: boolean | null;
  business_profile?: { name?: string | null } | null;
}

export async function upsertSellerFromStripeAccount(params: {
  userId: string;
  account: StripeAccountLike;
}): Promise<void> {
  await initAdmin();
  const adb = admin.firestore();
  const { userId, account } = params;

  const sellerRef = adb.collection("sellers").doc(userId);
  const sellerSnap = await sellerRef.get();
  const existing = sellerSnap.exists ? sellerSnap.data() : {} as Record<string, any>;

  await sellerRef.set(
    stripUndefined({
      ...existing,
      stripeAccountId: account.id,
      email: account.email ?? existing?.email ?? null,
      businessName: account.business_profile?.name ?? existing?.businessName ?? null,
      lastLoginAt: new Date().toISOString(),
      onboardingComplete: !!account.details_submitted,
      chargesEnabled: !!account.charges_enabled,
      payoutsEnabled: !!account.payouts_enabled,
      country: account.country ?? existing?.country ?? null,
      currency: account.default_currency ?? existing?.currency ?? null,
      accountType: account.type ?? existing?.accountType ?? null,
      businessType: account.business_type ?? existing?.businessType ?? null,
      updatedAt: new Date().toISOString(),
    }),
    { merge: true }
  );
}

export async function upsertStripeAccountsDoc(params: {
  accountId: string;
  userId: string;
  account: StripeAccountLike;
  extra?: Record<string, any>;
}): Promise<void> {
  await initAdmin();
  const adb = admin.firestore();
  const { accountId, userId, account, extra } = params;

  const ref = adb.collection("stripeAccounts").doc(accountId);
  const snap = await ref.get();
  const existing = snap.exists ? snap.data() : {} as Record<string, any>;

  const payload = stripUndefined({
    ...existing,
    userId,
    onboardingComplete: !!account.details_submitted,
    chargesEnabled: !!account.charges_enabled,
    payoutsEnabled: !!account.payouts_enabled,
    onboardingCompletedAt: account.details_submitted ? new Date().toISOString() : existing?.onboardingCompletedAt ?? null,
    lastUpdated: new Date().toISOString(),
    accountStatus: {
      detailsSubmitted: !!account.details_submitted,
      chargesEnabled: !!account.charges_enabled,
      payoutsEnabled: !!account.payouts_enabled,
      country: account.country ?? null,
      defaultCurrency: account.default_currency ?? null,
      businessType: account.business_type ?? null,
      type: account.type ?? null,
    },
    email: account.email ?? existing?.email ?? null,
    ...(extra || {}),
  });

  await ref.set(stripUndefined(payload), { merge: true });
}

export async function setUserStripeId(params: { userId: string; stripeAccountId: string }): Promise<void> {
  await initAdmin();
  const adb = admin.firestore();
  const { userId, stripeAccountId } = params;
  await adb.collection("users").doc(userId).set(
    {
      stripe_id: stripeAccountId,
      updated_at: new Date().toISOString(),
    },
    { merge: true }
  );
}

