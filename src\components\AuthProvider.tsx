"use client";

import { useEffect, useState } from "react";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { useRouter } from "next/navigation";
import { initFirebase } from "../../firebaseConfig";

export default function AuthWatcher({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [auth, setAuth] = useState<any>(null);

  useEffect(() => {
    // Initialize Firebase and set up auth listener only once
    const initializeAuth = async () => {
      try {
        const firebaseInstance = await initFirebase();
        const authInstance = getAuth(firebaseInstance.app);
        setAuth(authInstance);
      } catch (error) {
        console.error("Error initializing Firebase auth:", error);
      }
    };

    initializeAuth();
  }, []);

  useEffect(() => {
    if (!auth) return;

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        // Session is invalid, clear cached data
        localStorage.removeItem("user");
        sessionStorage.removeItem("user");

        // Optionally redirect
        router.push("/");
      } else {
        // Save user to localStorage if needed
        localStorage.setItem("user", JSON.stringify(user));
      }
    });

    return () => unsubscribe();
  }, [auth, router]);

  return <>{children}</>;
}
