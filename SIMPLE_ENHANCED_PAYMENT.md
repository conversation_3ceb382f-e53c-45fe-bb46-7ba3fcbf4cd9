# Simple Enhanced Payment System

A clean, simple implementation of enhanced Stripe payments with Apple Pay, Google Pay, and dynamic currency support - following your ideology of keeping things simple and straightforward.

## Features

### 💳 **Payment Methods**
- **Card Payments**: Traditional credit/debit cards
- **Apple Pay**: For iOS/macOS users
- **Google Pay**: For Android/Chrome users
- **Unified Interface**: Single component handles all payment methods

### 🌍 **Simple Currency Detection**
- **localStorage Priority**: Uses currency stored in `localStorage.getItem('currency')`
- **Simple Fallback**: Defaults to USD if no currency found
- **No Complex Logic**: No geolocation, email domain parsing, or complex detection
- **Easy to Control**: Just set `localStorage.setItem('currency', 'EUR')` to change currency

### 👤 **User Data Integration**
- **localStorage Only**: Gets user data from `localStorage.getItem('user')`
- **Profile Data**: Also checks for profile data in common localStorage keys
- **Auto-population**: Pre-fills payment forms with available user info
- **Simple Structure**: No complex merging or detection logic

## Components

### SimpleEnhancedCheckout
Clean, simple component that follows your coding style:

```tsx
import SimpleEnhancedCheckout from '@/components/SimpleEnhancedCheckout';

<SimpleEnhancedCheckout
  amount={2000} // Amount in cents
  currency="usd" // Optional, will use localStorage if not provided
  productName="Your Product"
  userId={user?.uid}
  sellerId="seller_123"
  orderId="order_456"
  isEscrow={false}
  enableApplePay={true}
  enableGooglePay={true}
  onSuccess={(paymentIntent) => console.log('Success!')}
  onError={(error) => console.error('Error:', error)}
/>
```

## Simple Currency Utilities

### Basic Functions
```tsx
import { getCurrency, setCurrency, getCurrencySymbol, formatAmount } from '@/lib/simpleCurrency';

// Get current currency from localStorage (defaults to USD)
const currency = getCurrency(); // 'USD'

// Set currency in localStorage
setCurrency('EUR'); // true if successful

// Get currency symbol
const symbol = getCurrencySymbol('EUR'); // '€'

// Format amount with currency
const formatted = formatAmount(10.50, 'USD'); // '$10.50'
```

### User Data Functions
```tsx
import { getUserData, getProfileData, getCombinedUserInfo } from '@/lib/simpleCurrency';

// Get user data from localStorage
const user = getUserData();

// Get profile data from localStorage
const profile = getProfileData();

// Get combined user info
const userInfo = getCombinedUserInfo();
```

## API Simplification

### Payment Intent APIs
Both `/api/create-payment-intent` and `/api/escrow/create-payment-intent` now accept:

```json
{
  "amount": 1000,
  "currency": "usd", // Simple currency code, no auto-detection
  "productName": "Product Name",
  "userId": "user_123",
  "sellerId": "seller_456",
  "orderId": "order_789",
  "userEmail": "<EMAIL>",
  "userName": "User Name",
  "paymentMethodType": "card"
}
```

**No complex currency detection** - just uses the provided currency or defaults to USD.

## Usage Examples

### Basic Payment
```tsx
"use client";

import { useState } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import SimpleEnhancedCheckout from '@/components/SimpleEnhancedCheckout';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function PaymentPage() {
  const [amount] = useState(20); // $20.00

  const handleSuccess = (paymentIntent: any) => {
    console.log('Payment successful:', paymentIntent);
    alert('Payment successful!');
  };

  const handleError = (error: string) => {
    console.error('Payment failed:', error);
    alert(`Payment failed: ${error}`);
  };

  return (
    <div className="max-w-md mx-auto mt-8 p-4">
      <h1 className="text-2xl font-bold mb-6">Simple Payment</h1>
      
      <Elements stripe={stripePromise}>
        <SimpleEnhancedCheckout
          amount={amount * 100} // Convert to cents
          productName="Demo Product"
          enableApplePay={true}
          enableGooglePay={true}
          onSuccess={handleSuccess}
          onError={handleError}
        />
      </Elements>
    </div>
  );
}
```

### Setting Currency
```tsx
// Set currency in localStorage (component will automatically use it)
import { setCurrency } from '@/lib/simpleCurrency';

// User selects EUR
setCurrency('EUR');

// Component will now use EUR for all payments
```

### Getting User Data
```tsx
// The component automatically gets user data from localStorage
// You can also access it manually:
import { getUserData } from '@/lib/simpleCurrency';

const user = getUserData();
console.log(user?.email); // <EMAIL>
```

## Test Pages

- `/payment/simple-demo` - Interactive demo with settings
- `/payment/buy-from-seller` - Updated with simple enhanced component

## Supported Currencies

USD, EUR, GBP, CAD, AUD, JPY, CHF, SEK, NOK, DKK, PLN, CZK, HUF, RUB, BRL, MXN, INR, SGD, HKD, KRW, THB, MYR, PHP, IDR, VND, TWD, AED, SAR, TRY, ZAR

## Key Differences from Complex Version

### ✅ **Simple Approach**
- Currency from localStorage only
- User data from localStorage only
- No geolocation detection
- No email domain parsing
- No complex fallback logic

### ✅ **Clean Code**
- Follows your coding style
- Minimal dependencies
- Easy to understand
- Easy to modify

### ✅ **Same Features**
- Apple Pay & Google Pay
- Dynamic currency
- User data integration
- Escrow support
- Error handling

## How It Works

1. **Currency**: Component checks `localStorage.getItem('currency')` or uses provided currency
2. **User Data**: Component checks `localStorage.getItem('user')` and profile data
3. **Payment Methods**: Automatically shows Apple Pay/Google Pay if available
4. **Fallbacks**: Simple defaults (USD currency, no user data if not found)

## Migration from Standard Checkout

Replace your existing checkout component:

```tsx
// Before
<EmbeddedCheckout
  amount={1000}
  currency="usd"
  onSuccess={handleSuccess}
  onError={handleError}
/>

// After
<SimpleEnhancedCheckout
  amount={1000}
  enableApplePay={true}
  enableGooglePay={true}
  onSuccess={handleSuccess}
  onError={handleError}
/>
```

The component will automatically:
- Use currency from localStorage
- Get user data from localStorage
- Show Apple Pay/Google Pay if available
- Handle all payment methods in one clean interface

**Simple, clean, and effective!** 🚀
