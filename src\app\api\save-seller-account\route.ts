import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { upsertStripeAccountsDoc, setUserStripeId } from '@/services/stripeConnectAdminService';

export async function POST(req: NextRequest) {
  try {
    const { sellerId, account, userId } = await req.json();

    // Determine the user ID to use (priority: sellerId > userId > auth)
    let finalUserId = sellerId || userId;

    if (!finalUserId) {
      finalUserId = await getUserIdFromRequest(req);
    }

    if (!finalUserId || !account) {
      return NextResponse.json(
        { error: 'Missing userId/sellerId or account ID' },
        { status: 400 }
      );
    }

    console.log('Saving seller account:', { userId: finalUserId, account });

    // Save/update the account in stripeAccounts collection via Admin SDK
    await upsertStripeAccountsDoc({
      accountId: account,
      userId: finalUserId,
      account: { id: account } as any,
      extra: {
        onboardingComplete: true,
        updatedAt: new Date().toISOString(),
      },
    });

    // Update user collection with stripe_id via Admin SDK
    try {
      await setUserStripeId({ userId: finalUserId, stripeAccountId: account });
      console.log('Successfully updated user with stripe_id via Admin SDK');
    } catch (userUpdateError) {
      console.error('Error updating user with stripe_id via Admin SDK:', userUpdateError);
      return NextResponse.json(
        { error: 'Failed to update user account' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Seller account saved successfully',
      userId: finalUserId,
      stripeAccountId: account
    });
  } catch (error) {
    console.error('Error saving seller account:', error);
    return NextResponse.json(
      { error: 'Failed to save seller account' },
      { status: 500 }
    );
  }
}
