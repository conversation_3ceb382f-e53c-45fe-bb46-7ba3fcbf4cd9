// command for importing backup
//✅ gcloud firestore import gs://<bucket-name>/<time-stamp> --async

const { onSchedule } = require("firebase-functions/v2/scheduler");
const { FirestoreAdminClient } = require("@google-cloud/firestore").v1;
const { Storage } = require("@google-cloud/storage");
const { getFirestore, Timestamp } = require("firebase-admin/firestore");
const admin = require("firebase-admin");
admin.initializeApp();

const client = new FirestoreAdminClient();
const db = getFirestore();

// const bucketName = "amuzn-webapp-dev-backup";
const bucketName = "amuzn-webapp-backup";
const bucket = `gs://${bucketName}`;
const storage = new Storage();

exports.scheduledFirestoreExport = onSchedule(
  // "every 5 minutes",
  "0 12 * * 0", // Every Sunday at 12:00 PM
  // { region: "us" },
  async () => {
    const projectId = process.env.GCP_PROJECT || process.env.GCLOUD_PROJECT;

    const databaseName = client.databasePath(projectId, "(default)");

    try {
      const [response] = await client.exportDocuments({
        name: databaseName,
        outputUriPrefix: bucket,
        collectionIds: [],
      });
      // console.log(`Started export operation: ${response?.name}`);

      // Cleanup old backups logic here...
      //       // ✅ Clean up old backups — only keep the latest export folder
      //       ////////////////////////////////////////////////////////////////////////////////////////////////

      const [files] = await storage.bucket(bucketName).getFiles({ autoPaginate: false });

      // Collect unique top-level folders (no Set)
      const folderNames = new Map<string, boolean>();

      for (const file of files) {
        const folder = file.name.split("/")[0];
        if (folder) folderNames.set(folder, true);
      }

      // Sort lexicographically, most recent first
      const sorted = Array.from(folderNames.keys()).sort().reverse();

      // Keep only the latest
      // const toDelete = sorted.slice(1);
      console.log({ sorted });

      for (const folder of sorted) {
        console.log(`Deleting old backup folder: ${folder}`);
        await storage.bucket(bucketName).deleteFiles({ prefix: `${folder}/` });
      }

      //       ////////////////////////////////////////////////////////////////////////////////////////////////
    } catch (error: any) {
      if (error) {
        try {
          console.error("Export failed:", JSON.stringify(error));
        } catch {
          console.error("Export failed:", error.message || error);
        }
      } else {
        console.error("Export failed: Unknown error");
      }
      throw new Error("Export operation failed");
    }
  }
);

/**
 *  🔥
 *  AUTO DECLINE
 *  -> after 2 days from the point of order placed if seller doesnt accept then it will be marked auto-decline
 *
 */

const BASE_URL = "https://amuzn-webapp-dev.vercel.app"; // DEV
// const BASE_URL ='https://www.amuzn.app'; // PROD

exports.autoDeclineStripe = onSchedule(
  // "every 5 minutes", // testing
  "every 60 minutes", // PROD
  // { region: "us" },
  async () => {
    try {
      const now = Timestamp.now();
      // const twoDaysAgo = Timestamp.fromMillis(now.toMillis() - 60 * 1000); // 1 min // DEV
      const twoDaysAgo = Timestamp.fromMillis(now.toMillis() - 2 * 24 * 60 * 60 * 1000); // 2 day // PROD

      const snapshot = await db
        .collection("orders")
        .where("status", "==", "NEW")
        .where("added_at", "<=", twoDaysAgo)
        .get();

      if (snapshot.empty) {
        console.log("No orders to auto-decline.");
        return;
      }

      for (const doc of snapshot.docs) {
        const orderData = doc.data();
        const orderId = doc.id;
        const janFirst2025 = Timestamp.fromDate(new Date("2025-01-01T00:00:00Z"));

        if (orderData.added_at.toMillis() < janFirst2025.toMillis()) {
          console.log("order is before 1 jan so ignoreing........");

          continue;
        }

        console.log(`Auto-declining order: ${orderId}`);

        await autoDeclineOrder(orderId, orderData);
      }
    } catch (error: any) {
      try {
        console.error("Auto-decline failed:", JSON.stringify(error));
      } catch {
        console.error("Auto-decline failed:", error.message || error);
      }
      throw new Error("Auto-decline operation failed");
    }
  }
);

async function autoDeclineOrder(orderId: string, orderData: any) {
  try {
    // change this
    const URL = `${BASE_URL}/api/stripe/cancel`;
    const currency = (orderData?.currency ?? "gbp").toLowerCase();
    const isUS = currency === "usd";
    console.log(URL, {
      orderId,
      chargeId: orderData.chargeId,
      paymentIntentId: orderData?.payment_intent_id,
      transactionId: orderData.transactionId,
      isUS,
      currency,
      title: "AUTO-DECLINED",
    });
    const response = await fetch(URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        orderId,
        chargeId: orderData.chargeId,
        paymentIntentId: orderData?.transactionId,
        transactionId: orderData.transactionId,
        isUS,
        currency,
        title: "AUTO-DECLINED",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Escrow cancel failed for order ${orderId}:`, errorText);
      return; // don't update Firestore if cancel failed
    }

    const result = await response.json();
    console.log("-> auto-decline ", { result });

    // update db status
    await db.collection("orders").doc(orderId).update({
      status: "AUTO-DECLINED",
      auto_declined_at: Timestamp.now(),
    });

    const userSnap = await db
      .collection("users")
      .where("services", "array-contains", orderData.serviceId)
      .limit(1)
      .get();

    if (userSnap.empty) {
      console.warn(`No seller found for serviceId: ${orderData.serviceId}`);
      return;
    }

    const sellerDoc = userSnap.docs[0];
    const sellerData = sellerDoc.data();
    const sellerName = sellerData.name || "Seller";

    const logPayload: any = {
      orderId: orderData?.uniqueId,
      title: "AUTO-DECLINED",
      date: Timestamp.now(),
      type: "Order status update",
      // from: "user",
      description: `Order was automatically declined because ${sellerName} did not respond to it in 48 hours`,
      previousStatus: orderData.status,
    };
    await db.collection("orders").doc(orderId).collection("activityLog").add(logPayload);

    // mail trigger

    const seller_details = await db.collection("users").doc(orderData?.userProfileId).get();
    if (!seller_details.exists) {
      throw new Error(`No user found for ID ${orderData?.userProfileId}`);
    }
    const sellerDetails = seller_details.data();

    const from = "<EMAIL>";
    const message = `Order was automatically declined because ${sellerName} did not respond to it in 48 hours`;
    console.log({ seller_details });

    let mailPayload = {
      to: sellerDetails?.email,
      from,
      message: {
        subject: `AMUZN Order status update - ${orderData?.uniqueId}: AUTO-DECLINED`,
        html: `
        <p>
          Hi ${sellerName} ,
        </p>
        <pre style="font-family: inherit; white-space: pre-wrap;">${message}</pre>
         <br/>
         <br/>
        <p>Thanks,<br/>Your AMUZN Team</p>
        `,
        text: "".replace(/<[^>]+>/g, ""),
      },
      createdAt: Timestamp.now(),
    };

    await db.collection("mail").add(mailPayload);
    console.log("mail queued for order ", orderId);

    console.log(`Order ${orderId} declined successfully`);
  } catch (err) {
    console.error(`Failed to decline order ${orderId}:`, err);
  }
}

/**
 *  🔥
 *  AUTO COMPLETE
 *  -> after 7 days of DELIVERY from seler it will auto complete and release the rest 80 % payment to seller
 *     and will mark status AUTO-COMPLETED
 *
 */

exports.autoCompleteOrders = onSchedule("every 60 minutes", async () => {
  try {
    const now = Timestamp.now();
    // const sevenDaysAgo = Timestamp.fromMillis(now.toMillis() - 60 * 1000); // 1min  // DEV
    const sevenDaysAgo = Timestamp.fromMillis(now.toMillis() - 7 * 24 * 60 * 60 * 1000); // 7 days // PROD

    const snapshot = await db
      .collection("orders")
      .where("status", "==", "DELIVERED")
      .where("lastDeliveredDate", "<=", sevenDaysAgo)
      .get();

    if (snapshot.empty) {
      console.log("No orders to auto-complete.");
      return;
    }

    for (const doc of snapshot.docs) {
      const orderData = doc.data();
      const orderId = doc.id;

      console.log(`Auto-completing order: ${orderId}`);
      await autoCompleteOrder(orderId, orderData);
    }
  } catch (error: any) {
    try {
      console.error("Auto-complete failed:", JSON.stringify(error));
    } catch {
      console.error("Auto-complete failed:", error.message || error);
    }
    throw new Error("Auto-complete operation failed");
  }
});

async function autoCompleteOrder(orderId: string, orderData: any) {
  try {
    // change this
    const URL = `${BASE_URL}/api/escrow/release`;

    const response = await fetch(URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        orderId,
        stage: "completed",
        chargeId: orderData?.chargeId,
        title: "AUTO-COMPLETED",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Escrow completing failed for order ${orderId}:`, errorText);
      return; // don't update Firestore if cancel failed
    }

    const result = await response.json();
    console.log("-> auto-COMPLETED ", { result });

    // update db status
    await db.collection("orders").doc(orderId).update({
      status: "AUTO-COMPLETED",
      auto_completed_at: Timestamp.now(),
    });

    const logPayload: any = {
      orderId: orderData?.uniqueId,
      title: "AUTO-COMPLETED",
      date: Timestamp.now(),
      type: "Order status update",
      // from: "user",
      description: `The order has Auto-Completed.\n\nNote that in accordance with the Terms of Service, if no action is taken by the Buyer to change the status of an order after it has been marked as “Delivered” by the Seller, the order will automatically complete and marked as “Auto-Completed” seven (7) days after the order Due Date. \n\nPrior to this time, the Buyer has the opportunity to action a Delivered order by either accepting, revising it, or requesting a refund by changing the order status to “Completed”, “Revision Request”, or “Refund Request”, respectively.\n\nYour AMUZN Team\<EMAIL>\nwww.amuzn.com`,
      previousStatus: orderData.status,
    };

    await db.collection("orders").doc(orderId).collection("activityLog").add(logPayload);

    const userSnap = await db
      .collection("users")
      .where("services", "array-contains", orderData.serviceId)
      .limit(1)
      .get();

    if (userSnap.empty) {
      console.warn(`No seller found for serviceId: ${orderData.serviceId}`);
      return;
    }

    const sellerDoc = userSnap.docs[0];
    const sellerData = sellerDoc.data();
    const sellerName = sellerData.name || "Seller";

    let activityLogInfoPayload = {
      orderId: orderData?.uniqueId,
      date: Timestamp.now(),
      type: "Order info",
      title: "AUTO-COMPLETED",
      description: `${sellerName} received 80% of order service cost.`,
    };
    await db
      .collection("orders")
      .doc(orderId)
      .collection("activityLog")
      .add(activityLogInfoPayload);

    const buyer_details = await db.collection("users").doc(orderData?.profileId).get();
    if (!buyer_details.exists) {
      throw new Error(`No user found for ID ${orderData?.userProfileId}`);
    }
    const buyerDetails = buyer_details.data();

    const from = "<EMAIL>";
    const message = `The order has Auto-Completed.\n\nNote that in accordance with the Terms of Service, if no action is taken by the Buyer to change the status of an order after it has been marked as “Delivered” by the Seller, the order will automatically complete and marked as “Auto-Completed” seven (7) days after the order Due Date. \n\nPrior to this time, the Buyer has the opportunity to action a Delivered order by either accepting, revising it, or requesting a refund by changing the order status to “Completed”, “Revision Request”, or “Refund Request”, respectively.\n\nYour AMUZN Team\<EMAIL>\nwww.amuzn.com`;
    console.log({ buyer_details });

    let mailPayload = {
      to: buyerDetails?.email,
      from,
      message: {
        subject: `AMUZN Order status update - ${orderData?.uniqueId}: AUTO-COMPLETED`,
        html: `
          <p>
          Hi ${sellerName} ,
        </p>
        <br/>
        <pre style="font-family: inherit; white-space: pre-wrap;">${message}</pre>
        <br/>
        <br/>
      <p>Thanks,<br/>Your AMUZN Team</p>
        `,
        text: "".replace(/<[^>]+>/g, ""),
      },
      createdAt: Timestamp.now(),
    };
    console.log({ mailPayload });

    await db.collection("mail").add(mailPayload);
    console.log("mail queued for order ", orderId);
    console.log(`Order ${orderId} auto completed successfully`);
  } catch (error) {}
}
