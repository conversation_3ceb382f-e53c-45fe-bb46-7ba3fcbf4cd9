import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { getDoc, doc } from 'firebase/firestore';
import { initFirebase } from '../../../../../firebaseConfig';
import { upsertStripeAccountsDoc, setUserStripeId, StripeAccountLike } from '@/services/stripeConnectAdminService';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    // Parse request body first to get user data
    let body: any = {};
    let userId: string | undefined;
    let email: string | undefined;

    try {
      body = await req.json();
      userId = body.userId;
      email = body.email;
    } catch (error) {
      console.error('Error parsing request body:', error);
    }

    // If no user ID in body, try to get from auth
    if (!userId) {
      const authUserId = await getUserIdFromRequest(req);
      if (authUserId) {
        userId = authUserId;
      }
    }

    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to create a Stripe account.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    console.log('Creating Stripe account for user:', userId, 'with email:', email);

    // Check if user already has a Stripe account
    try {
      const { db } = await initFirebase();
      const userDocRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.stripe_id) {
          console.log('User already has Stripe account:', userData.stripe_id);

          // Verify the account still exists in Stripe
          try {
            const existingAccount = await stripe.accounts.retrieve(userData.stripe_id);

            // Return existing account info
            return NextResponse.json({
              accountId: existingAccount.id,
              userId,
              existing: true,
              message: 'User already has a Stripe account'
            });
          } catch (stripeError) {
            console.log('Existing Stripe account not found, creating new one');
            // Continue to create new account if the old one doesn't exist
          }
        }
      }
    } catch (error) {
      console.log('Error checking existing account, proceeding with creation:', error);
    }

    // 1. Create a new Express account for the seller
    const accountData: any = { type: 'express' };

    // Add email to account creation if provided
    if (email) {
      accountData.email = email;
    }

    const account = await stripe.accounts.create(accountData);

    // 2. Save account via Admin SDK and update user
    try {
      await upsertStripeAccountsDoc({
        accountId: account.id,
        userId: userId,
        account: account as StripeAccountLike,
        extra: {
          createdAt: new Date().toISOString(),
          onboardingComplete: false,
          accountType: 'express',
          ...(email ? { email } : {}),
        }
      });

      await setUserStripeId({ userId, stripeAccountId: account.id });

      console.log('Saved Stripe account via Admin SDK:', { userId, accountId: account.id, email });
    } catch (firebaseError) {
      console.error('Error saving via Admin SDK:', firebaseError);
      // Continue with onboarding even if Firebase save fails
    }

    // 3. Create an onboarding link
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${origin}/`,
      return_url: `${origin}/`,
      type: 'account_onboarding',
    });

    return NextResponse.json({
      url: accountLink.url,
      accountId: account.id,
      userId
    });
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    );
  }
}
