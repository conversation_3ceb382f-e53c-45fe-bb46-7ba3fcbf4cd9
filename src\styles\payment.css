/* Enhanced Payment Styles */

/* Payment Request Button (Apple Pay/Google Pay) */
.PaymentRequestButton {
  height: 48px;
  border-radius: 6px;
  margin-bottom: 16px;
  width: 100% !important;
}

/* Ensure the button takes full width */
.PaymentRequestButton > div {
  width: 100% !important;
  height: 48px !important;
}

/* Payment Request Button Element styling */
.payment-request-button {
  width: 100%;
  margin-bottom: 1rem;
}

.payment-request-button .StripeElement {
  width: 100% !important;
  height: 48px !important;
  border-radius: 6px;
}

/* Style the payment request button container */
.payment-request-container {
  margin-bottom: 24px;
}

/* Divider styling */
.payment-divider {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.payment-divider::before,
.payment-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #e5e7eb;
}

.payment-divider span {
  padding: 0 12px;
  color: #6b7280;
  font-size: 14px;
}

/* Enhanced payment form styling */
.enhanced-payment-form {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Payment element container */
.payment-element-container {
  margin-bottom: 16px;
}

/* Error message styling */
.payment-error {
  margin-top: 16px;
  padding: 12px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  border-radius: 6px;
  font-size: 14px;
}

/* Success message styling */
.payment-success {
  margin-top: 16px;
  padding: 12px;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
  border-radius: 6px;
  font-size: 14px;
}

/* Payment button styling */
.payment-button {
  width: 100%;
  margin-top: 24px;
  background-color: #2563eb;
  color: white;
  font-weight: 600;
  padding: 12px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.payment-button:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.payment-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Payment info section */
.payment-info {
  margin-top: 16px;
  font-size: 14px;
  color: #6b7280;
}

.payment-info p {
  margin-bottom: 4px;
}

.payment-info .escrow-notice {
  color: #2563eb;
  font-weight: 500;
}

/* Loading spinner */
.payment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px;
}

.payment-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Demo page specific styles */
.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px 24px;
}

.demo-section {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1f2937;
}

.demo-section h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #374151;
}

/* Form controls */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.form-checkbox {
  margin-right: 8px;
}

.form-radio {
  margin-right: 8px;
}

/* Button variants */
.btn-primary {
  background-color: #2563eb;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

/* Responsive design */
@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }
  
  .demo-section {
    padding: 16px;
  }
  
  .enhanced-payment-form {
    padding: 16px;
  }
}
