// Simple currency utilities - no complex detection, just localStorage and basic formatting

export const currencySymbols: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  CAD: 'C$',
  AUD: 'A$',
  JPY: '¥',
  CHF: 'CHF',
  SEK: 'kr',
  NOK: 'kr',
  DKK: 'kr',
  PLN: 'zł',
  CZK: 'Kč',
  HUF: 'Ft',
  RUB: '₽',
  BRL: 'R$',
  MXN: '$',
  INR: '₹',
  SGD: 'S$',
  HKD: 'HK$',
  KRW: '₩',
  THB: '฿',
  MYR: 'RM',
  PHP: '₱',
  IDR: 'Rp',
  VND: '₫',
  TWD: 'NT$',
  AED: 'د.إ',
  SAR: '﷼',
  TRY: '₺',
  ZAR: 'R'
};

/**
 * Get currency from localStorage or default to USD
 */
export const getCurrency = (): string => {
  try {
    const stored = localStorage.getItem('currency');
    if (stored && currencySymbols[stored.toUpperCase()]) {
      return stored.toUpperCase();
    }
  } catch (error) {
    console.warn('Could not access localStorage for currency');
  }
  return 'USD';
};

/**
 * Set currency in localStorage
 */
export const setCurrency = (currency: string): boolean => {
  try {
    const upperCurrency = currency.toUpperCase();
    if (currencySymbols[upperCurrency]) {
      localStorage.setItem('currency', upperCurrency);
      return true;
    }
  } catch (error) {
    console.warn('Could not save currency to localStorage');
  }
  return false;
};

/**
 * Get currency symbol
 */
export const getCurrencySymbol = (currency?: string): string => {
  const curr = currency || getCurrency();
  return currencySymbols[curr.toUpperCase()] || '$';
};

/**
 * Format amount with currency symbol
 */
export const formatAmount = (amount: number, currency?: string): string => {
  const curr = currency || getCurrency();
  const symbol = getCurrencySymbol(curr);
  return `${symbol}${amount.toFixed(2)}`;
};

/**
 * Get user data from localStorage
 */
export const getUserData = () => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.warn('Could not get user data from localStorage');
    return null;
  }
};

/**
 * Get profile data from localStorage (check common keys)
 */
export const getProfileData = () => {
  try {
    // Check common profile keys
    const keys = ['profile', 'userProfile', 'profileData'];
    
    for (const key of keys) {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          const parsed = JSON.parse(data);
          if (parsed && typeof parsed === 'object') {
            return parsed;
          }
        } catch (e) {
          // Continue to next key
        }
      }
    }
    
    return null;
  } catch (error) {
    console.warn('Could not get profile data from localStorage');
    return null;
  }
};

/**
 * Get combined user info from localStorage
 */
export const getCombinedUserInfo = () => {
  const userData = getUserData();
  const profileData = getProfileData();
  
  return {
    ...userData,
    ...profileData
  };
};
