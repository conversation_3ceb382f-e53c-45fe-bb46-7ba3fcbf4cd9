import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { getUserIdFromRequest } from "@/lib/auth/serverAuth";
import admin from "firebase-admin";
import { initAdmin } from "../../../../../firebaseAdminConfig";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { accountId } = body;

    if (!accountId) {
      return NextResponse.json({ error: "Account ID is required" }, { status: 400 });
    }

    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        {
          error: "Authentication required. Please log in to complete onboarding.",
          code: "AUTH_REQUIRED",
        },
        { status: 401 }
      );
    }

    console.log("Checking onboarding completion for account:", accountId, "user:", userId);

    // 1. Retrieve account details from <PERSON><PERSON> to check onboarding status
    const account = await stripe.accounts.retrieve(accountId);

    if (!account) {
      return NextResponse.json({ error: "Stripe account not found" }, { status: 404 });
    }

    // 2. Update Firebase stripeAccounts collection with onboarding status using Admin SDK
    try {
      await initAdmin();
      const adb = admin.firestore();

      // Get existing account data via Admin
      const stripeAccountRef = adb.collection("stripeAccounts").doc(accountId);
      const stripeAccountDoc = await stripeAccountRef.get();

      if (!stripeAccountDoc.exists) {
        return NextResponse.json({ error: "Account not found in database" }, { status: 404 });
      }

      const existingData = stripeAccountDoc.data() as any;

      // Verify this account belongs to the authenticated user
      if (existingData.userId !== userId) {
        return NextResponse.json({ error: "Unauthorized access to this account" }, { status: 403 });
      }

      // Update onboarding status based on Stripe account details
      const updatedData: any = {
        ...existingData,
        onboardingComplete: account.details_submitted || false,
        chargesEnabled: account.charges_enabled || false,
        payoutsEnabled: account.payouts_enabled || false,
        onboardingCompletedAt: account.details_submitted ? new Date().toISOString() : null,
        lastUpdated: new Date().toISOString(),
        // Store additional account details
        accountStatus: {
          detailsSubmitted: account.details_submitted,
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
          country: account.country,
          defaultCurrency: account.default_currency,
          businessType: account.business_type,
          type: account.type,
        },
      };

      // Add email if available from Stripe account
      if ((account as any).email) {
        updatedData.email = (account as any).email;
      }

      await stripeAccountRef.set(updatedData, { merge: true });

      console.log("Successfully updated onboarding status in Firebase (Admin):", {
        accountId,
        userId,
        onboardingComplete: updatedData.onboardingComplete,
        chargesEnabled: updatedData.chargesEnabled,
        payoutsEnabled: updatedData.payoutsEnabled,
      });

      return NextResponse.json({
        success: true,
        message: "Onboarding status updated successfully",
        accountId,
        userId,
        onboardingStatus: {
          complete: updatedData.onboardingComplete,
          chargesEnabled: updatedData.chargesEnabled,
          payoutsEnabled: updatedData.payoutsEnabled,
          detailsSubmitted: account.details_submitted,
        },
        account: {
          id: account.id,
          email: account.email,
          country: account.country,
          defaultCurrency: account.default_currency,
          businessType: account.business_type,
          type: account.type,
        },
      });
    } catch (firebaseError) {
      console.error("Error updating Firebase (Admin):", firebaseError);
      return NextResponse.json(
        { error: "Failed to update onboarding status in database" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error checking onboarding completion:", error);
    return NextResponse.json({ error: "Failed to check onboarding completion" }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const accountId = searchParams.get("accountId");

    if (!accountId) {
      return NextResponse.json({ error: "Account ID is required" }, { status: 400 });
    }

    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        {
          error: "Authentication required.",
          code: "AUTH_REQUIRED",
        },
        { status: 401 }
      );
    }

    // Get onboarding status from Firebase (Admin)
    try {
      await initAdmin();
      const adb = admin.firestore();
      const stripeAccountRef = adb.collection("stripeAccounts").doc(accountId);
      const stripeAccountDoc = await stripeAccountRef.get();

      if (!stripeAccountDoc.exists) {
        return NextResponse.json({ error: "Account not found in database" }, { status: 404 });
      }

      const accountData = stripeAccountDoc.data() as any;

      // Verify this account belongs to the authenticated user
      if (accountData.userId !== userId) {
        return NextResponse.json({ error: "Unauthorized access to this account" }, { status: 403 });
      }

      return NextResponse.json({
        success: true,
        accountId,
        userId,
        onboardingStatus: {
          complete: accountData.onboardingComplete || false,
          chargesEnabled: accountData.chargesEnabled || false,
          payoutsEnabled: accountData.payoutsEnabled || false,
          completedAt: accountData.onboardingCompletedAt || null,
        },
        accountStatus: accountData.accountStatus || null,
        lastUpdated: accountData.lastUpdated || null,
      });
    } catch (firebaseError) {
      console.error("Error getting onboarding status from Firebase (Admin):", firebaseError);
      return NextResponse.json(
        { error: "Failed to get onboarding status from database" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error getting onboarding status:", error);
    return NextResponse.json({ error: "Failed to get onboarding status" }, { status: 500 });
  }
}
