import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getEscrowTransactionByOrderId, releaseEscrowStage } from '@/services/transactionService';
import { getStripeInstanceByCurrency } from '@/lib/stripe';
import { getOrderById } from '@/services/ordersServices';
import { OrdersHandlerManager } from '@/lib/api-gateway-handlers/order-handlers';
import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Helper function to validate charge and get currency
async function validateChargeAndGetCurrency(chargeId: string, transactionCurrency: string) {
  try {
    // Automatically select the correct Stripe instance based on transaction currency
    const { stripeInstance } = getStripeInstanceByCurrency(transactionCurrency);
    const charge = await stripeInstance.charges.retrieve(chargeId);

    // Also check the balance transaction currency
    let balanceTransactionCurrency = null;
    if (charge.balance_transaction) {
      try {
        const balanceTransaction = await stripeInstance.balanceTransactions.retrieve(charge.balance_transaction as string);
        balanceTransactionCurrency = balanceTransaction.currency;
        console.log('Balance transaction details:', {
          id: balanceTransaction.id,
          currency: balanceTransaction.currency,
          amount: balanceTransaction.amount,
          net: balanceTransaction.net
        });
      } catch (btError) {
        console.error('Error retrieving balance transaction:', btError);
      }
    }

    return {
      success: true,
      currency: charge.currency,
      amount: charge.amount,
      status: charge.status,
      balanceTransactionId: charge.balance_transaction,
      balanceTransactionCurrency
    };
  } catch (error) {
    console.error('Error retrieving charge:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const { orderId, stage, chargeId ,
       from,
  sellerName,
  title,
  userName,
  description,
  reason,
  uniqueOrderId,
  newDueDate,
  orderInfoStatus ,
  loggedInUser
      
    } = await request.json();
    console.log({orderId , stage , chargeId});
    

    if (!orderId || !stage) {
      return NextResponse.json({ error: 'Missing required fields: orderId, stage' }, { status: 400 });
    }

    if (!['accept', 'delivered', 'completed'].includes(stage)) {
      return NextResponse.json({ error: 'Invalid stage. Must be: accept, delivered, or completed' }, { status: 400 });
    }

    // 1) Load order (for chargeId/profileId)
    const orderRes = await OrdersHandlerManager.getInstance().getOrderById(orderId);
    // getOrderById(orderId);
    console.log({orderRes});
    
    if (!orderRes.success || !(orderRes as any).order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }
    const order: any = (orderRes as any).order;

    // 2) Resolve chargeId: request -> order.chargeId -> PI->charges (by payment_intent_id)
    let effectiveChargeId = chargeId || order?.chargeId;
    if (!effectiveChargeId && order?.payment_intent_id) {
      const { stripeInstance } = getStripeInstanceByCurrency(order?.isUS ? 'usd' : 'gbp');
      const charges = await stripeInstance.charges.list({ payment_intent: order.payment_intent_id, limit: 1 });
      effectiveChargeId = charges.data?.[0]?.id;
    }
    if (!effectiveChargeId) {
      return NextResponse.json({ error: 'chargeId not found. Provide chargeId or ensure order has chargeId/payment_intent_id' }, { status: 400 });
    }

    // 3) Retrieve the charge to validate currency and amount
    // Prefer currency based on order context: If you track currency on order, use it; else use charge currency as truth
    const { stripeInstance } = getStripeInstanceByCurrency(order?.isUS ? 'usd' : 'gbp');
    const charge = await stripeInstance.charges.retrieve(effectiveChargeId);
    if (!charge?.currency || !charge?.amount) {
      return NextResponse.json({ error: 'Invalid charge details from Stripe' }, { status: 500 });
    }

    // 4) Check existing transfers from Stripe to prevent overage
    const existingTransfersAll = await stripeInstance.transfers.list({ limit: 100 });
    const transfersFromCharge = existingTransfersAll.data.filter((t) => {
      const st = (t as any).source_transaction as string | undefined;
      return st === effectiveChargeId;
    });
    const transfersForOrder = existingTransfersAll.data.filter((t) => t.metadata?.orderId === orderId);

    const priorTransferredFromStripe = transfersFromCharge.reduce((sum, t) => sum + t.amount, 0);
    const remainingGross = Math.max(0, charge.amount - priorTransferredFromStripe);

    // Compute seller-eligible total (subtotal * 0.84)
    const subtotalMinor = Math.round(charge.amount / 1.04);
    const sellerAmountMinor = Math.round(subtotalMinor * 0.84);

    // How much has already been transferred to seller for this order (based on metadata)
    const sellerPriorTransferred = transfersForOrder.reduce((sum, t) => sum + t.amount, 0);
    const sellerRemaining = Math.max(0, sellerAmountMinor - sellerPriorTransferred);

    console.log('Transfer calculation:', {
      chargeAmount: charge.amount,
      priorTransferredFromStripe,
      remainingGross,
      sellerAmountMinor,
      sellerPriorTransferred,
      sellerRemaining,
      stage
    });

    if (sellerRemaining <= 0 || remainingGross <= 0) {
      return NextResponse.json({ error: 'No amount remaining to transfer for this order' }, { status: 400 });
    }

    // Check if this stage was already released by looking at transfer metadata
    const stageAlreadyReleased = transfersForOrder.some((t) =>
      t.metadata?.stage === stage && t.metadata?.orderId === orderId
    );
    if (stageAlreadyReleased) {
      return NextResponse.json({ error: 'This stage has already been released' }, { status: 400 });
    }

    let transferAmountCents = 0;
    if (stage === 'completed') {
      // For the final 80% stage, transfer exactly the seller remaining, guard by gross remaining
      transferAmountCents = Math.min(sellerRemaining, remainingGross);
    } else {
      const intendedSellerStage = Math.round(sellerAmountMinor * 0.10);
      transferAmountCents = Math.min(intendedSellerStage, sellerRemaining, remainingGross);
    }
    console.log({transferAmountCents});
    

    // 5) Ensure payment is captured (capture PI if not captured)
    if (!charge.captured) {
      if (!charge.payment_intent) {
        return NextResponse.json({ error: 'Cannot capture payment. No payment intent on charge.' }, { status: 400 });
      }
      try {
        await stripeInstance.paymentIntents.capture(charge.payment_intent as string);
      } catch (e: any) {
        return NextResponse.json({ error: 'Failed to capture payment', details: e?.message || 'Unknown error' }, { status: 400 });
      }
    }

    // 6) Destination account via GetUserStripeId(order.profileId)
    // const { GetUserStripeId } = await import('@/services/usersServices');
    // is this imp ??
    const destinationAccount = await UsersHandlerManager.getInstance().GetUserStripeId(order?.profileId)
    //  GetUserStripeId(order.profileId);
    if (!destinationAccount) {
      return NextResponse.json({ error: 'Seller Stripe account ID not found' }, { status: 400 });
    }

    // 7) Create transfer from captured charge to seller
    const transfer = await stripeInstance.transfers.create({
      amount: transferAmountCents,
      currency: charge.currency,
      source_transaction: effectiveChargeId,
      destination: destinationAccount,
      metadata: {
        orderId,
        stage,
        sellerId: order.profileId,
        escrowStage: stage,
        transferredAt: new Date().toISOString(),
   from,
  sellerName,
  title,
  userName,
  description,
  reason,
  uniqueOrderId,
  newDueDate,
  orderInfoStatus ,
  loggedInUser        
      },
    });

    // is this impp ?
    // // 8) Mark stage released on the order (DB write via OrdersServices)
    // try {
    //   const { markEscrowStageReleased } = await import('@/services/ordersServices');
    //   await markEscrowStageReleased(orderId, stage, transferAmountCents, transfer.id);
    // } catch (e) {
    //   console.warn('Failed to mark stage released on order:', e);
    // }

    return NextResponse.json({
      success: true,
      transferId: transfer.id,
      amount: transferAmountCents,
      amountInMajorUnit: transferAmountCents / 100,
      currency: charge.currency,
      stage,
      orderId,
      message: `Released ${stage} payment of ${(transferAmountCents / 100).toFixed(2)} ${charge.currency.toUpperCase()}`,
    });
  } catch (error) {
    console.error('Escrow release error (no-transactions path):', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// async function POST_OLD(request: NextRequest) {
//   try {
//     const {
//       orderId,
//       stage, // 'accept', 'delivered', 'completed'
//       chargeId
//     } = await request.json();

//     // Validate required fields
//     if (!orderId || !stage) {
//       return NextResponse.json({
//         error: 'Missing required fields: orderId, stage'
//       }, { status: 400 });
//     }

//     // Validate stage
//     if (!['accept', 'delivered', 'completed'].includes(stage)) {
//       return NextResponse.json({
//         error: 'Invalid stage. Must be: accept, delivered, or completed'
//       }, { status: 400 });
//     }

//     // Get escrow transaction
//     const transactionResult = await getEscrowTransactionByOrderId(orderId);
//     if (!transactionResult.success || !transactionResult.transaction) {
//       return NextResponse.json({
//         error: 'Escrow transaction not found for this order'
//       }, { status: 404 });
//     }

//     const transaction = transactionResult.transaction;

//     // Check if this stage is already released
//     const stageData = transaction.escrowStages?.find(s => s.stage === stage);
//     if (!stageData) {
//       return NextResponse.json({
//         error: 'Stage not found in escrow transaction'
//       }, { status: 400 });
//     }

//     if (stageData.status === 'released') {
//       return NextResponse.json({
//         error: 'This stage has already been released'
//       }, { status: 400 });
//     }

//     // Resolve chargeId if not provided: try order.chargeId, then PI->charges
//     let effectiveChargeId = chargeId;
//     try {
//       const orderRes = await getOrderById(orderId);
//       if (orderRes.success && (orderRes as any).order) {
//         effectiveChargeId = effectiveChargeId || (orderRes as any).order?.chargeId;
//       }
//     } catch {}

//     if (!effectiveChargeId && transaction.stripePaymentIntentId) {
//       try {
//         const { stripeInstance } = getStripeInstanceByCurrency(transaction.currency);
//         const charges = await stripeInstance.charges.list({ payment_intent: transaction.stripePaymentIntentId, limit: 1 });
//         if (charges.data?.length) effectiveChargeId = charges.data[0].id;
//       } catch (e) {
//         console.warn('Failed to derive chargeId from payment intent:', e);
//       }
//     }

//     if (!effectiveChargeId) {
//       return NextResponse.json({ error: 'chargeId not found. Provide chargeId or ensure order has chargeId/PI' }, { status: 400 });
//     }

//     // Get the original charge to ensure currency consistency
//     const chargeValidation = await validateChargeAndGetCurrency(effectiveChargeId, transaction.currency);
//     if (!chargeValidation.success) {
//       return NextResponse.json({
//         error: 'Failed to retrieve original charge information',
//         details: chargeValidation.error
//       }, { status: 500 });
//     }

//     if (!chargeValidation.currency || chargeValidation.amount == null) {
//       return NextResponse.json({
//         error: 'Invalid charge details returned from Stripe',
//         details: {
//           currency: chargeValidation.currency,
//           amount: chargeValidation.amount,
//           status: chargeValidation.status
//         }
//       }, { status: 500 });
//     }

//     const chargeCurrency: string = chargeValidation.currency;
//     console.log('Original charge currency:', chargeCurrency);
//     console.log('Transaction currency:', transaction.currency);
//     console.log('Charge amount:', chargeValidation.amount);
//     console.log('Charge status:', chargeValidation.status);
//     console.log('Charge ID being used:', chargeId);
//     console.log('Transaction session ID:', transaction.stripeSessionId);
//     console.log('Balance transaction ID:', chargeValidation.balanceTransactionId);
//     console.log('Balance transaction currency:', chargeValidation.balanceTransactionCurrency);

//     // Ensure currency consistency (case-insensitive)
//     const chargeCurrencyNorm = (chargeCurrency || '').toLowerCase();
//     const transactionCurrencyNorm = (transaction.currency || '').toLowerCase();
//     if (chargeCurrencyNorm !== transactionCurrencyNorm) {
//       console.error('Currency mismatch:', { chargeCurrency, transactionCurrency: transaction.currency });
//       return NextResponse.json({
//         error: `Currency mismatch: charge is in ${chargeCurrency} but transaction is in ${transaction.currency}. Please ensure the escrow transaction was created with the same currency as the original payment.`
//       }, { status: 400 });
//     }

//     // Check if balance transaction currency differs from charge currency
//     if (chargeValidation.balanceTransactionCurrency && chargeValidation.balanceTransactionCurrency !== chargeCurrency) {
//       console.error('Balance transaction currency mismatch:', {
//         chargeCurrency,
//         balanceTransactionCurrency: chargeValidation.balanceTransactionCurrency
//       });
//       return NextResponse.json({
//         error: `Balance transaction currency mismatch: charge is in ${chargeCurrency} but balance transaction is in ${chargeValidation.balanceTransactionCurrency}. This may be due to currency conversion settings in your Stripe account.`,
//         details: {
//           chargeId,
//           chargeCurrency,
//           balanceTransactionCurrency: chargeValidation.balanceTransactionCurrency,
//           balanceTransactionId: chargeValidation.balanceTransactionId,
//           suggestion: 'Check your Stripe account currency conversion settings or use a charge that was processed in the same currency as your account default.'
//         }
//       }, { status: 400 });
//     }

//     // Validate charge amount vs transfer amount
//     // Note: stageData.amount is already in smallest currency unit (cents/pence)
//     const transferAmountCents = Math.round(stageData.amount);
//     console.log('Transfer validation:', {
//       transferAmountCents,
//       chargeAmount: chargeValidation.amount,
//       stageAmount: stageData.amount,
//       stageAmountInMajorUnit: stageData.amount / 100,
//       transactionTotalAmount: transaction.amount,
//       transactionTotalInMajorUnit: transaction.amount / 100,
//       stage,
//       percentage: stageData.percentage
//     });

//     if (transferAmountCents > chargeValidation.amount!) {
//       return NextResponse.json({
//         error: `Transfer amount (${transferAmountCents} cents) exceeds charge amount (${chargeValidation.amount} cents)`,
//         details: {
//           transferAmount: stageData.amount,
//           transferAmountInMajorUnit: stageData.amount / 100,
//           transferAmountCents,
//           chargeAmount: chargeValidation.amount,
//           chargeAmountInMajorUnit: chargeValidation.amount / 100,
//           chargeCurrency: chargeCurrency,
//           transactionAmount: transaction.amount,
//           transactionAmountInMajorUnit: transaction.amount / 100,
//           transactionCurrency: transaction.currency,
//           stage,
//           percentage: stageData.percentage,
//           suggestion: 'Please verify you are using the correct charge ID that matches this escrow transaction amount'
//         }
//       }, { status: 400 });
//     }

//     // Step 1: Check charge status and capture if needed
//     console.log('🔄 Step 1: Checking charge status...');
//     const charge = await stripe.charges.retrieve(chargeId);

//     if (!charge.captured) {
//       console.log('⚠️ Charge not captured yet. For escrow, we need to capture the full payment first.');

//       // Get the payment intent to capture the full amount
//       if (charge.payment_intent) {
//         console.log('🔄 Capturing full payment via Payment Intent...');
//         try {
//           const capturedPaymentIntent = await stripe.paymentIntents.capture(charge.payment_intent as string);
//           console.log('✅ Full payment captured via Payment Intent:', {
//             id: capturedPaymentIntent.id,
//             status: capturedPaymentIntent.status,
//             amount_received: capturedPaymentIntent.amount_received
//           });
//         } catch (captureError) {
//           console.error('❌ Failed to capture payment intent:', captureError);
//           return NextResponse.json({
//             error: 'Failed to capture payment',
//             details: captureError instanceof Error ? captureError.message : 'Unknown capture error',
//             chargeId,
//             paymentIntentId: charge.payment_intent
//           }, { status: 400 });
//         }
//       } else {
//         return NextResponse.json({
//           error: 'Cannot capture payment. No payment intent found.',
//           details: 'This charge was not created via Payment Intent and cannot be captured for escrow.',
//           chargeId
//         }, { status: 400 });
//       }
//     }

//     console.log('✅ Payment is captured and held in Stripe. Creating transfer for escrow stage...');

//     // Step 2: Create direct transfer to seller (from captured payment held in Stripe)
//     console.log('🔄 Step 2: Creating transfer to seller...');

//     // Use the correct Stripe instance based on transaction currency
//     const { stripeInstance, isUS } = getStripeInstanceByCurrency(transaction.currency);
//     console.log(`🌍 Using ${isUS ? 'US' : 'International'} Stripe instance for ${transaction.currency.toUpperCase()} currency`);
//     try {
//       // Find order uniqueId for cleaner metadata (optional)
//       let orderUniqueId: string | undefined;
//       try {
//         const orderRes = await getOrderById(orderId);
//         if (orderRes.success && (orderRes as any).order?.uniqueId) {
//           orderUniqueId = (orderRes as any).order.uniqueId as string;
//         }
//       } catch {}

//       // Resolve seller destination from usersServices using seller uid (transaction.sellerId or order.profileId)
//       let destinationAccount: string | null = null;
//       try {
//         const orderRes = await getOrderById(orderId);
//         const sellerUid = transaction.sellerId || ((orderRes.success && (orderRes as any).order?.profileId) as string | undefined);
//         if (sellerUid) {
//           const { GetUserStripeId } = await import('@/services/usersServices');
//           destinationAccount = await GetUserStripeId(sellerUid);
//         }
//       } catch (e) {
//         console.warn('Failed to resolve seller destination via GetUserStripeId:', e);
//       }
//       if (!destinationAccount) {
//         return NextResponse.json({ error: 'Seller Stripe account ID not found' }, { status: 400 });
//       }

//       const transfer = await stripeInstance.transfers.create({
//         amount: transferAmountCents, // Amount is already in smallest currency unit
//         currency: chargeCurrency, // Use the charge currency to ensure consistency
//         source_transaction: effectiveChargeId,
//         destination: destinationAccount,
//         metadata: {
//           orderId,
//           ...(orderUniqueId ? { orderUniqueId } : {}),
//           stage,
//           transactionId: transaction.id,
//           sellerId: transaction.sellerId!,
//           escrowStage: stage,
//           transferredAt: new Date().toISOString()
//         }
//       });

//       // Update escrow stage status
//       const releaseResult = await releaseEscrowStage(
//         transaction.id,
//         stage,
//         transfer.id
//       );

//       if (!releaseResult.success) {
//         return NextResponse.json({
//           error: 'Failed to update escrow stage status'
//         }, { status: 500 });
//       }

//       return NextResponse.json({
//         success: true,
//         transferId: transfer.id,
//         amount: stageData.amount,
//         amountInMajorUnit: stageData.amount / 100,
//         currency: chargeCurrency,
//         stage,
//         orderId,
//         message: `Successfully released ${stage} stage payment of ${(stageData.amount / 100).toFixed(2)} ${chargeCurrency.toUpperCase()}`
//       });

//     } catch (stripeError) {
//       console.error('Stripe transfer error:', stripeError);

//       // Provide specific error messages for common Stripe errors
//       let errorMessage = 'Failed to create Stripe transfer';
//       let statusCode = 500;

//       if (stripeError && typeof stripeError === 'object' && 'type' in stripeError) {
//         const error = stripeError as any;

//         if (error.type === 'StripeInvalidRequestError') {
//           statusCode = 400;
//           if (error.code === 'account_invalid') {
//             errorMessage = 'Seller account is invalid or not properly set up';
//           } else if (error.param === 'source_transaction') {
//             if (error.message.includes('currency')) {
//               errorMessage = `Currency mismatch: The charge ID you provided is from a different currency transaction. ${error.message}. Please use the GET endpoint with ?findChargeId=true to find the correct charge ID for this escrow transaction.`;
//             } else {
//               errorMessage = `Invalid source transaction: ${error.message}`;
//             }
//           } else if (error.param === 'currency') {
//             errorMessage = `Currency error: ${error.message}`;
//           } else {
//             errorMessage = `Invalid request: ${error.message}`;
//           }
//         } else if (error.type === 'StripePermissionError') {
//           statusCode = 403;
//           errorMessage = 'Insufficient permissions to transfer to this account';
//         } else {
//           errorMessage = error.message || errorMessage;
//         }
//       }

//       return NextResponse.json({
//         error: errorMessage,
//         type: stripeError && typeof stripeError === 'object' && 'type' in stripeError ? (stripeError as any).type : 'unknown',
//         chargeId,
//         transferAmount: stageData.amount,
//         currency: chargeCurrency
//       }, { status: statusCode });
//     }

//   } catch (error) {
//     console.error('Escrow release error:', error);
//     const errorMessage = error instanceof Error ? error.message : 'Unknown error';
//     return NextResponse.json({ error: errorMessage }, { status: 500 });
//   }
// }

// GET endpoint to check escrow status and find correct charge ID
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');
    const findChargeId = searchParams.get('findChargeId') === 'true';
    const showTransfers = searchParams.get('showTransfers') === 'true';
    if (!orderId) {
      return NextResponse.json({
        error: 'orderId parameter is required'
      }, { status: 400 });
    }

    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // If requested, try to find the correct charge ID
    let suggestedChargeId = null;
    let chargeSearchResults = null;

    if (findChargeId && transaction.stripeSessionId) {
      try {
        // Use the correct Stripe instance based on transaction currency
        const { stripeInstance, isUS } = getStripeInstanceByCurrency(transaction.currency);
        console.log(`🔍 Finding charge ID using ${isUS ? 'US' : 'International'} Stripe instance for ${transaction.currency.toUpperCase()} currency`);

        // Get the payment intent directly
        if (transaction.stripePaymentIntentId) {
          const paymentIntent = await stripeInstance.paymentIntents.retrieve(transaction.stripePaymentIntentId);

          // Get charges for this payment intent
          const charges = await stripeInstance.charges.list({
            payment_intent: paymentIntent.id,
            limit: 1
          });

          if (charges.data && charges.data.length > 0) {
            const charge = charges.data[0];
            suggestedChargeId = charge.id;
            chargeSearchResults = {
              chargeId: charge.id,
              amount: charge.amount,
              currency: charge.currency,
              status: charge.status,
              amountMatches: charge.amount === transaction.amount,
              currencyMatches: charge.currency === transaction.currency
            };
          }
        }
      } catch (error) {
        console.error('Error finding charge ID:', error);
      }
    }

    // Get transfer details if requested
    let transferDetails = null;
    if (showTransfers && transaction.escrowStages) {
      transferDetails = [];
      for (const stage of transaction.escrowStages) {
        if (stage.stripeTransferId) {
          try {
            const transfer = await stripe.transfers.retrieve(stage.stripeTransferId);
            transferDetails.push({
              transferId: transfer.id,
              amount: transfer.amount,
              amountInMajorUnit: transfer.amount / 100,
              currency: transfer.currency,
              destination: transfer.destination,
              created: new Date(transfer.created * 1000).toISOString(),
              status: transfer.reversed ? 'reversed' : 'completed',
              stage: stage.stage,
              description: transfer.description,
              metadata: transfer.metadata
            });
          } catch (error) {
            console.error(`Error retrieving transfer ${stage.stripeTransferId}:`, error);
            transferDetails.push({
              transferId: stage.stripeTransferId,
              error: 'Failed to retrieve transfer details',
              stage: stage.stage
            });
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      transaction: {
        id: transaction.id,
        orderId: transaction.orderId,
        currentStage: transaction.currentStage,
        totalAmount: transaction.amount,
        subtotal: transaction.subtotal,
        transactionFee: transaction.transactionFee,
        platformCommission: transaction.platformCommission,
        sellerAmount: transaction.sellerAmount,
        stripeSessionId: transaction.stripeSessionId,
        currency: transaction.currency,
        escrowStages: transaction.escrowStages?.map(stage => ({
          stage: stage.stage,
          percentage: stage.percentage,
          amount: stage.amount,
          status: stage.status,
          releasedAt: stage.releasedAt,
          stripeTransferId: stage.stripeTransferId
        }))
      },
      ...(suggestedChargeId && {
        suggestedChargeId,
        chargeSearchResults,
        message: 'Found suggested charge ID based on the transaction session'
      }),
      ...(transferDetails && { transferDetails })
    });

  } catch (error) {
    console.error('Escrow status error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
