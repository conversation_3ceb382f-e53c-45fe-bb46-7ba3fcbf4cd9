"use client";
import React, { useEffect, useState } from "react";
import {
  AnyPublicationMetadataFieldsFragment,
  CommentFieldsFragment,
  CommentRankingFilterType,
  HiddenCommentsType,
  LensTransactionStatusType,
  LimitType,
  Maybe,
  MetadataAttribute,
  MetadataAttributeType,
  NewPublicationStatsDocument,
  PaginatedPublicationsResult,
  PaginatedPublicationsTagsResult,
  ProfileWhoReactedResult,
  PublicationCommentOn,
  PublicationMetadataLicenseType,
  PublicationMetadataMainFocusType,
  PublicationReactionType,
  PublicationReportingFraudSubreason,
  PublicationReportingReason,
  PublicationReportingSpamSubreason,
  PublicationsQuery,
  useAddReactionMutation,
  useCreateMomokaCommentTypedDataMutation,
  useFeedHighlightsQuery,
  useLensTransactionStatusQuery,
  useProfileQuery,
  usePublicationQuery,
  usePublicationsQuery,
  useReportPublicationMutation,
  useWhoReactedPublicationQuery,
  WhoReactedPublicationQuery,
} from "@/graphql/generated";
import { useFollow } from "@/lib/useFollow";

import { useUnFollow } from "@/lib/useUnfollow";
import { useReaction } from "@/lib/useReaction";
import { useComment } from "@/lib/useCommen";
import { useBookMark } from "@/lib/useBookMark";
import { FollowerManager } from "@/services/followServices";
import {
  getUserById,
  isEmailVerified,
  refreshUser,
  sendVerificationEmail,
} from "@/services/usersServices";
import { getEventsByCategory } from "@/services/eventsServices";
import ProtectedRoute from "@/components/ProtectedRoute";

type Status = "follow" | "unfollow";

export default function Home() {
  const [searchValue, setSearchValue] = useState<string>(""); // State for search input
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);
  const [currentFeedCursor, setCurrentFeedCursor] = useState<string | null>(null);

  const [Txn_id, setTxn_id] = useState<string | null>(null);
  const [CurrentStatus, setCurrentStatus] = useState<Status>("follow");

  const commentResp = useComment();
  const bookmarkResp = useBookMark();

  const { data: transactionData, refetch: refetchTransactionStatus } =
    useLensTransactionStatusQuery({
      request: { forTxId: Txn_id },
    });

  const { data: profileData } = useProfileQuery({
    request: {
      forProfileId: "0x0215d1",
    },
  });

  const { data: feedData } = useFeedHighlightsQuery({
    request: {
      where: {
        for: "0x08cfd6",
        metadata: {
          mainContentFocus: [
            PublicationMetadataMainFocusType.Image,
            PublicationMetadataMainFocusType.Video,
          ],
        },
      },
      limit: LimitType.Fifty,
      cursor: currentFeedCursor,
    },
  });

  // console.log({ feedData });

  //
  const { isLoading, data, error } = usePublicationQuery({
    request: {
      // post:"39130437942447979329874165024976872993139868756996365088967062590380725348778"
      forId: "0x89dc-0x1796-DA-dc1a7342",
    },
  });

  const {
    isLoading: commentsLoading,
    data: comments,
    error: commentsError,
  } = usePublicationsQuery({
    request: {
      where: {
        commentOn: {
          id: "0x05-0x27b7-DA-dccf390c",
          hiddenComments: HiddenCommentsType.Hide,
          ranking: {
            filter: CommentRankingFilterType.Relevant,
          },
        },
      },
      limit: LimitType.TwentyFive,
      cursor: currentCursor,
    },
  });

  // console.log({comments});

  const {
    isLoading: liked_loading,
    data: liked_data,
    error: liked_error,
  } = useWhoReactedPublicationQuery({
    request: {
      for: "0x89dc-0x1796-DA-dc1a7342",
      limit: LimitType.Ten,
      cursor: currentCursor,
    },
  });

  const loadNextPage = () => {
    const nextCursor = liked_data?.whoReactedPublication.pageInfo.next;
    if (nextCursor) {
      setCurrentCursor(nextCursor);
    }
  };

  const { mutateAsync: followUser, isSuccess, data: followData } = useFollow();
  const unfollow = useUnFollow();

  const addReaction = useReaction();

  const { mutateAsync: reportPost } = useReportPublicationMutation();

  // useEffect(() => {
  //   // console.log({ isSuccess, followData });

  //   if (isSuccess) {
  //     setTxn_id(
  //       // @ts-ignore
  //       followData.txId
  //     );

  //     // console.log({ Txn_id });
  //   }
  // }, [isSuccess, followData, Txn_id]);

  // useEffect(() => {
  //   if (!Txn_id) return;

  //   const intervalId = setInterval(async () => {
  //     if (
  //       transactionData?.lensTransactionStatus?.status ===
  //         LensTransactionStatusType.Complete ||
  //       transactionData?.lensTransactionStatus?.status ===
  //         LensTransactionStatusType.Failed
  //     ) {
  //       alert(`${CurrentStatus} successfully ✅`);
  //       clearInterval(intervalId);
  //     } else {
  //       // console.log("fetch");
  //       await refetchTransactionStatus();
  //     }
  //   }, 1500);

  //   return () => clearInterval(intervalId); // Cleanup on unmount
  // }, [Txn_id, transactionData, refetchTransactionStatus]);

  const sendEmail = async () => {
    const response = await sendVerificationEmail();
    // console.log(response);
  };

  const checkVerification = async () => {
    await refreshUser(); // Refresh user info before checking
    const response = isEmailVerified();
    // console.log({ response });
    if (response.verified) {
      // verified
    } else {
      //
    }
  };

  const test_func = async () => {
    try {
      // const resp = await FollowerManager.getInstance().GetFollowersByUserId(
      //   "nqypcn9V9QYBF4zMjhNZk4WUfQz1"
      // );
      // const resp1 = await FollowerManager.getInstance().GetFollowingsByUserId("nqypcn9V9QYBF4zMjhNZk4WUfQz1");
      // const resp = await FollowerManager.getInstance().GetFollowingsByUserId("W5437xR475cbUigqs5BlqWStrZw1");
      // console.log({resp});
      // const resp = await FollowerManager.getInstance().FollowByUserId({src_id:"Y3f83F4U7uf8K3ZVardNPj3mrxf2" , dest_id:"nqypcn9V9QYBF4zMjhNZk4WUfQz1"})
      // console.log({resp});
      //  const resp =  await getEventsByUserId("Y3f83F4U7uf8K3ZVardNPj3mrxf2");
      const resp = await getEventsByCategory("Art", undefined, {
        user_id: ["rJ9JkrjX5FdqQ9BUYFUDTtlsYnu2"],
      });
      //p
      // const resp = await getServicesByCategory("Art");
      //  const resp =  await getServicesByUserId("rJ9JkrjX5FdqQ9BUYFUDTtlsYnu2");
      // console.log({resp});
      // const resp  = await toggleBookMarks("Y3f83F4U7uf8K3ZVardNPj3mrxf2","ydJZ358NfYeyVVskH9Fi",false);

      console.log({ resp });
    } catch (error: any) {
      // console.log({ error });
      alert(error?.message ?? "something went wrong!");
    }
  };

  const getLensUserId = async () => {
    try {
      const response = await getUserById("YHcuMxBfl1R7J562fUFwIghBqQw2");
      // console.log({ response });

      // const resp = await getId({id:"0x08cfd6"}); // send lens_code(web3) or user_id (web2)
      // console.log({resp});

      /**
       *  Response :
       *  {
       *  lens_code :
       *  user_id :
       *  }
       */

      // if (!resp?.lens_code) // for getting web3 lens_id
      // if (!resp?.user_id) // for getting web2 id
    } catch (error) {
      console.log({ error });
    }
  };

  const deleteUser = async () => {
    try {
      // const user = auth.currentUser;
      // console.log({user});
      // if(!user?.uid){
      //   alert("user not found")
      //   return;
      // }
      // const resp = await user?.delete();
      // await deleteUserDetails({user_id:user.uid})
      // console.log({resp});
      // await logOut()
      // window.location.reload();
    } catch (error: any) {
      console.log({ error });
      alert(error?.message ?? "somthing went wrong");
    }
  };

  return (
    <>
      <ProtectedRoute>
        <button onClick={checkVerification}>checking email verification</button>

        <div
          onClick={async () => {
            await getLensUserId();
          }}
        >
          get user id
        </div>

        <div onClick={test_func}>click test</div>

        <div className="flex flex-col">Post details</div>

        {/* <button onClick={handleFetchClick}>Fetch Status</button> */}

        <div className="flex gap-4">
          <h1>post:0x05-0x27b7-DA-dccf390c</h1>

          {/* <button
          onClick={async () => {
            const resp = await addReaction(
              "0x05-0x27b7-DA-dccf390c",
              PublicationReactionType.Upvote,
              "like"
            );
            // console.log({ resp });
          }}
        >
          Like post{" "}
        </button> */}

          {/* <button
          onClick={async () => {
            const resp = await addReaction(
              "0x05-0x27b7-DA-dccf390c",
              PublicationReactionType.Downvote,
              "dislike"
            );
            // console.log({ resp });
          }}
        >
          Dislike post{" "}
        </button> */}

          <button
            onClick={async () => {
              const resp = await commentResp(
                // "0x01c845-0x1713",
                "0x01c845-0x170f-DA-1bc2fd6b",
                "apple mango",
                true // momoka
              );
              // console.log({ resp });
            }}
          >
            {" "}
            comment post (https://hey.xyz/posts/0x01c845-0x170f-DA-1bc2fd6b)
          </button>

          <button
            onClick={async () => {
              const resp = await bookmarkResp("0x01490d-0x0e-DA-325d1d5e", true);
            }}
          >
            {" "}
            bookmark post (https://hey.xyz/posts/0x0200a5-0x0273-DA-bf8d4fa4)
          </button>

          <div>
            <h1>Report post 0xe30c-0x0561-DA-07f0bdda</h1>

            <button
              onClick={async () => {
                // await loginUser();
                // "0xe30c-0x0561-DA-07f0bdda",
                // "apple",
                // PublicationReportingReason.Spam,
                // PublicationReportingSpamSubreason.LowSignal
                // const resp = await reportPost({
                //   request: {
                //     for: "0xe30c-0x0561-DA-07f0bdda",
                //     additionalComments: "apple",
                //     reason: {
                //       spamReason: {
                //         reason: "FRAUD" as PublicationReportingReason,
                //         subreason: "IMPERSONATION" as PublicationReportingFraudSubreason,
                //       },
                //     },
                //   },
                // });
                // console.log({ resp });
              }}
            >
              Report
            </button>
          </div>
        </div>

        <div>
          naruto @0x0215d1
          <span className="font-extrabold">
            {" "}
            {profileData?.profile?.operations.isFollowedByMe.value ? "Following" : "Follow"}{" "}
          </span>
        </div>
        <div className="flex gap-2">
          <div
            onClick={async () => {
              setCurrentStatus("follow");
              await followUser("0x0215d1");
            }}
          >
            Follow User
          </div>

          <div
            onClick={async () => {
              setCurrentStatus("unfollow");
              const resp = await unfollow("0x0215d1");
              setTxn_id(
                // @ts-ignore
                resp.txId
              );
            }}
          >
            Follow User
          </div>
        </div>

        <div className="flex">
          {
            // @ts-ignore
            data?.publication?.metadata?.content
          }
        </div>

        {
          // @ts-ignore
          data?.publication?.metadata?.asset?.image?.raw?.uri && (
            // @ts-ignore

            <img src={data?.publication?.metadata?.asset?.image?.raw?.uri || ""} />
          )
        }

        <div className="flex  gap-4">
          <span className="flex font-bold">
            {" "}
            comments:
            {
              // @ts-ignore
              data?.publication.stats?.comments
            }
          </span>
          <span className="flex font-bold">
            {" "}
            mirrors:
            {
              // @ts-ignore
              data?.publication.stats?.mirrors
            }
          </span>
          <span className="flex font-bold">
            {" "}
            quotes:
            {
              // @ts-ignore
              data?.publication.stats?.quotes
            }
          </span>
          <span className="flex font-bold">
            {" "}
            likes:
            {
              // @ts-ignore
              data?.publication.stats?.reactions
            }
          </span>
        </div>

        <div className="px-8 mb-8">
          <div className="flex gap-4">
            <div className="flex p-8 gap-2 cursor-pointer" onClick={loadNextPage}>
              Next
            </div>
          </div>

          <div>
            comments:
            {comments?.publications.items?.map(
              (current: PublicationsQuery["publications"]["items"][0], index) => {
                return (
                  <div className="flex gap-2 my-2 flex-col">
                    <div className=" font-bold">
                      {index + 1} By :{" "}
                      {
                        // @ts-ignore
                        current?.by?.handle?.fullHandle
                      }
                    </div>
                    <div className="flex">
                      {
                        //@ts-ignore
                        current?.metadata?.content
                      }
                    </div>
                  </div>
                );
              }
            )}
            <div></div>
          </div>

          <span className="flex font-extrabold"> liked list : </span>

          <div className="flex flex-col gap-2 ">
            {liked_data?.whoReactedPublication.items.map(
              (curr: WhoReactedPublicationQuery["whoReactedPublication"]["items"][0]) => {
                return <div className="flex font-bold">{curr.profile.handle?.fullHandle}</div>;
              }
            )}
          </div>
        </div>

        {}
      </ProtectedRoute>
    </>
  );
}
