"use client";
import { getAllPostsOptimized } from "@/services/postService";
import { useState, useEffect, useRef, useCallback } from "react";
import { Timestamp } from "firebase/firestore";
import Link from "next/link";
import PostSkeletonLoader from "./PostSkeletonLoader";
import LazyMedia from "@/components/LazyMedia";
import { useFilter } from "@/context/FilterContext";
import { generateFileUrl } from "@/lib/utils";

const NewPostMobileView = (props: any) => {
  const { filters, getServiceFilters } = useFilter();
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastVisibleTimestamp, setLastVisibleTimestamp] = useState<Timestamp | null>(null);
  const [hasMorePosts, setHasMorePosts] = useState(true);
  const pageSize = props.isScroll ? 10 : 5; // Number of posts to fetch per page
  const loaderRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!baseUrl) return undefined;

  //   if (!postFile) {
  //     return undefined;
  //   }

  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //     return postFile;
  //   }

  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  const fetchPosts = useCallback(
    async (isInitialLoad: boolean = false) => {
      if (loading) return;

      setLoading(true);
      console.time("time");

      try {
        // If it's initial load, reset the posts array and lastVisibleTimestamp
        if (isInitialLoad) {
          setPosts([]);
          setLastVisibleTimestamp(null);
        }

        const serviceFilters = getServiceFilters();

        const resp = await getAllPostsOptimized({
          category_name: props.category === "My Feed" ? "my-feed" : props.category,
          userId: props.category === "My Feed" ? props?.user_id : "",
          pageSize,
          lastVisibleTimestamp: isInitialLoad ? undefined : lastVisibleTimestamp || undefined,
          filters: serviceFilters,
        });

        // console.log({ resp });

        if (resp.posts && resp.posts.length > 0) {
          // If it's an initial load, replace posts, otherwise append them
          setPosts((prevPosts) => (isInitialLoad ? resp.posts : [...prevPosts, ...resp.posts]));

          // Update the lastVisibleTimestamp for next pagination
          const lastPost = resp.posts[resp.posts.length - 1];
          if (lastPost && lastPost.timestamp) {
            // If timestamp is already a Timestamp object, use it directly
            // Otherwise, convert it to a Timestamp
            const timestamp =
              lastPost.timestamp instanceof Timestamp
                ? lastPost.timestamp
                : Timestamp.fromDate(new Date(lastPost.timestamp));

            setLastVisibleTimestamp(timestamp);
          }

          // If we got fewer posts than pageSize, we've reached the end
          setHasMorePosts(resp.posts.length >= pageSize);
        } else {
          setHasMorePosts(false);
        }
      } catch (error) {
        console.error("Error fetching posts:", error);
      } finally {
        setLoading(false);
        console.timeEnd("time");
      }
    },
    [filters]
  );

  // Load initial posts when component mounts or when props.category changes
  useEffect(() => {
    fetchPosts(true);
  }, [props.category]);

  // Filter posts based on props.isScroll
  const displayedPosts = props.isScroll
    ? posts.slice(5) // Skip the first 5 items when isScroll is true
    : posts;

  // Setup horizontal scroll listener - ONLY if props.isScroll is true
  useEffect(() => {
    // Skip setting up scroll listener if isScroll is not enabled
    if (!props.isScroll) return;

    const handleScroll = () => {
      if (!scrollContainerRef.current || loading || !hasMorePosts) return;

      const container = scrollContainerRef.current;
      const { scrollLeft, scrollWidth, clientWidth } = container;

      // Check if we're near the right edge (within 100px)
      const isNearRightEdge = scrollLeft + clientWidth >= scrollWidth - 100;

      if (isNearRightEdge) {
        fetchPosts(false);
      }
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, [loading, hasMorePosts, props.isScroll]);

  // Setup intersection observer for the right edge element - ONLY if props.isScroll is true
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      // Skip handling intersection if isScroll is not enabled
      if (!props.isScroll) return;

      const [target] = entries;
      if (target.isIntersecting && hasMorePosts && !loading) {
        fetchPosts(false);
      }
    },
    [hasMorePosts, loading, props.isScroll]
  );

  useEffect(() => {
    // Skip setting up observer if isScroll is not enabled
    if (!props.isScroll) return;

    const options = {
      root: null,
      rootMargin: "0px 20px 0px 0px", // Margin to the right
      threshold: 0.1,
    };

    const observer = new IntersectionObserver(handleObserver, options);

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [handleObserver, props.isScroll]);

  return (
    <div className="p-0 w-full">
      {displayedPosts.length > 0 ? (
        <div
          ref={scrollContainerRef}
          className={`flex ${props.isScroll ? "overflow-x-auto" : "overflow-x-hidden"} scrollbar-hide scroll-smooth`}
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          {displayedPosts.map((post, index) => (
            <div
              key={post.id || index}
              className={props.isScroll ? "flex-shrink-0 mr-[2px]" : "flex-shrink-0 mr-[2px]"}
            >
              {/* Media display */}
              <Link className="" href={`/browse/${post.category}/${post.id}%20${post.user_id}`}>
                {post.mediaType === "image" && post.postFile ? (
                  <LazyMedia
                    src={generateFileUrl(post.postFile) || "/assets/noimg.png"}
                    alt={post.title || `Post ${index}`}
                    type="image"
                    className="min-h-[85px] min-w-[85px] max-h-[85px] max-w-[85px] object-cover border-2 aspect-square"
                    style={{
                      borderColor: props.borderColor,
                    }}
                    placeholderClassName="bg-gray-100"
                  />
                ) : post.mediaType === "video" && post.postFile ? (
                  <LazyMedia
                    src={generateFileUrl(post.postFile)}
                    type="video"
                    className="min-h-[85px] min-w-[85px] max-h-[85px] max-w-[85px] object-cover border-2 aspect-square"
                    style={{
                      borderColor: props.borderColor,
                    }}
                    showPlayIcon={true}
                    playIconClassName="top-2 right-2"
                    controls={false}
                    autoPlay={false}
                    muted={true}
                  />
                ) : null}
              </Link>
            </div>
          ))}

          {/* Right edge loader element - only show if isScroll is enabled */}
          {props.isScroll && (
            <div
              ref={loaderRef}
              className="flex-shrink-0 flex items-center justify-center h-[85px] min-w-[40px]"
            >
              {loading && (
                // <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
                <PostSkeletonLoader
                  isScroll={props.isScroll}
                  borderColor={props.borderColor}
                  count={5} // Always show 5 items per row
                />
              )}
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-0">
          {loading ? (
            // <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
            <PostSkeletonLoader
              isScroll={props.isScroll}
              borderColor={props.borderColor}
              count={5} // Always show 5 items per row
            />
          ) : props.isScroll && posts.length <= 5 ? (
            "No additional posts found"
          ) : (
            "No posts found"
          )}
        </div>
      )}

      {/* End of posts message - only visible when fully loaded and if isScroll is enabled */}
      {/* {props.isScroll && !loading && !hasMorePosts && posts.length > 0 && (
        <p className="text-center text-gray-500 text-xs mt-2">No more posts</p>
      )} */}
    </div>
  );
};

export default NewPostMobileView;
