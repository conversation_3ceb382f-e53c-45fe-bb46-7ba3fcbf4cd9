"use client";

import React, { useEffect, useState } from "react";
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  useStripe,
  useElements,
  PaymentElement,
} from "@stripe/react-stripe-js";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface EmbeddedCheckoutProps {
  amount: number;
  currency?: string;
  productName?: string;
  userId?: string;
  sellerId?: string;
  orderId?: string;
  isEscrow?: boolean;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}

// Internal checkout form component that uses Stripe hooks
function CheckoutForm({
  amount,
  currency = "usd",
  productName = "Product",
  userId,
  sellerId,
  orderId,
  isEscrow = false,
  onSuccess,
  onError
}: EmbeddedCheckoutProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [errorMessage, setErrorMessage] = useState<string>();
  const [clientSecret, setClientSecret] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const createPaymentIntent = async () => {
      try {
        const endpoint = isEscrow ? "/api/escrow/create-payment-intent" : "/api/create-payment-intent";
        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            amount,
            currency,
            productName,
            userId,
            sellerId,
            orderId,
            isEscrow
          }),
        });

        const data = await response.json();
        if (data.clientSecret) {
          setClientSecret(data.clientSecret);
        } else {
          throw new Error(data.error || "Failed to create payment intent");
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : "Unknown error";
        setErrorMessage(message);
        onError?.(message);
      }
    };

    createPaymentIntent();
  }, [amount, currency, productName, userId, sellerId, orderId, isEscrow]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);

    if (!stripe || !elements) {
      return;
    }

    const { error: submitError } = await elements.submit();

    if (submitError) {
      setErrorMessage(submitError.message);
      setLoading(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      clientSecret,
      confirmParams: {
        return_url: `${window.location.origin}/payment-success?amount=${amount}${orderId ? `&order_id=${orderId}` : ''}`,
        payment_method_data: {
          billing_details: {
            name: userId || "Customer",
          },
        },
      },
      redirect: "never" as any
    });

    if (error) {
      setErrorMessage(error.message);
      onError?.(error.message || "Payment failed");
    } else if (paymentIntent && paymentIntent.status === "succeeded") {
      onSuccess?.(paymentIntent);
    }

    setLoading(false);
  };

  if (!clientSecret || !stripe || !elements) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="bg-white p-2 rounded-md">
      {clientSecret && <PaymentElement />}

      {errorMessage && <div>{errorMessage}</div>}

      <button
        disabled={!stripe || loading}
        className="text-white w-full p-5 bg-black mt-2 rounded-md font-bold disabled:opacity-50 disabled:animate-pulse"
      >
        {!loading ? `Pay $${(amount / 100).toFixed(2)}` : "Processing..."}
      </button>
    </form>
  );
}

// Main component that wraps CheckoutForm with Elements provider
const EmbeddedCheckout = (props: EmbeddedCheckoutProps) => {
  const options = {
    mode: 'payment' as const,
    amount: props.amount,
    currency: props.currency || 'usd',
    appearance: {
      theme: 'stripe' as const,
    },
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2 text-gray-900">{props.productName}</h2>
        <div className="text-gray-600">
          <p className="text-lg font-medium">
            {(props.currency || 'usd').toUpperCase()} {(props.amount / 100).toFixed(2)}
          </p>
          {props.isEscrow && (
            <p className="text-sm text-blue-600 mt-1">
              🔒 Escrow Payment - Funds held securely
            </p>
          )}
        </div>
      </div>

      <Elements options={options} stripe={stripePromise}>
        <CheckoutForm {...props} />
      </Elements>
    </div>
  );
};

export default EmbeddedCheckout;